package com.autoagent.ai_live_master.webSocket.controller;

import com.autoagent.ai_live_master.webSocket.service.TokenService;
import com.autoagent.ai_live_master.webSocket.vo.TokenRequest;
import com.autoagent.ai_live_master.webSocket.vo.TokenResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Token管理", description = "Token相关接口")
@RestController
@RequestMapping("/api/token")
public class TokenController {

    @Autowired
    private TokenService tokenService;

    @Operation(summary = "获取Token", description = "根据AccessKey ID和Secret获取Token")
    @PostMapping("/get")
    public TokenResponse getToken(@RequestBody TokenRequest request) {
        return tokenService.getToken(request.getAccessKeyId(), request.getAccessKeySecret());
    }
} 