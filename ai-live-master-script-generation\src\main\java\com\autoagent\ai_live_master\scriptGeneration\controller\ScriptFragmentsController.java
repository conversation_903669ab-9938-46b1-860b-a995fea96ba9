package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptGeneration.dto.ScriptFragmentsDTO;
import com.autoagent.ai_live_master.scriptGeneration.service.ScriptFragmentsService;
import com.autoagent.ai_live_master.scriptGeneration.vo.ScriptFragmentsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "14-话术片段管理", description = "话术片段的生成和管理相关接口")
@RestController
@RequestMapping("/script-fragments")
public class ScriptFragmentsController {

    @Autowired
    private ScriptFragmentsService scriptFragmentsService;

    @Operation(summary = "根据选择的内容生成片段话术", description = "根据用户选择的内容生成相应的话术片段")
    @PostMapping
    public ApiResponse<String> generateFragment(
            @Parameter(description = "话术片段生成参数", required = true)
            @RequestBody ScriptFragmentsVO vo) {
        try {
            ScriptFragmentsDTO dto = BeanConverter.convert(vo, ScriptFragmentsDTO.class);
            dto.setUserId(UserContext.getCurrentUserId());
            String fragment = scriptFragmentsService.generateScriptFragments(dto);
            return ApiResponse.success(fragment);
        } catch (Exception e) {
            log.error("生成话术片段失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "生成话术片段失败: " + e.getMessage());
        }
    }
}