package com.autoagent.ai_live_master.scriptGeneration.service.impl;

import com.autoagent.ai_live_master.scriptGeneration.dto.UserDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.User;
import com.autoagent.ai_live_master.scriptGeneration.mapper.UserMapper;
import com.autoagent.ai_live_master.scriptGeneration.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public UserDTO getUserById(Integer id) {
        User user = this.getById(id);
        return UserDTO.fromEntity(user);
    }

    @Override
    public UserDTO getUserByUserid(String userid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserid, userid);
        User user = this.getOne(queryWrapper);
        return UserDTO.fromEntity(user);
    }

    @Override
    public List<UserDTO> getAllUsers() {
        List<User> users = this.list();
        return users.stream()
                .map(UserDTO::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Page<UserDTO> getPageUsers(Integer pageNum, Integer pageSize) {
        Page<User> page = new Page<>(pageNum, pageSize);
        Page<User> userPage = this.page(page);
        
        Page<UserDTO> dtoPage = new Page<>();
        dtoPage.setTotal(userPage.getTotal());
        dtoPage.setCurrent(userPage.getCurrent());
        dtoPage.setSize(userPage.getSize());
        dtoPage.setRecords(userPage.getRecords().stream()
                .map(UserDTO::fromEntity)
                .collect(Collectors.toList()));
        return dtoPage;
    }

    @Override
    public UserDTO createUser(User user) {
        // 检查用户名是否已存在
        UserDTO existingUser = getUserByUserid(user.getUserid());
        if (existingUser != null) {
            // 用户名已存在，返回null
            return null;
        }
        // 用户名不存在，保存新用户
        this.save(user);
        return UserDTO.fromEntity(user);
    }

    @Override
    public UserDTO updateUser(User user) {
        // 检查用户是否存在
        User existingUser = this.getById(user.getId());
        if (existingUser == null) {
            // 用户不存在，返回null
            return null;
        }
        // 用户存在，执行更新
        this.updateById(user);
        // 重新获取更新后的用户信息
        return getUserById(user.getId());
    }

    @Override
    public boolean deleteUser(Integer id) {
        // 检查用户是否存在
        User existingUser = this.getById(id);
        if (existingUser == null) {
            // 用户不存在，返回false
            return false;
        }
        // 用户存在，执行删除
        return this.removeById(id);
    }
}