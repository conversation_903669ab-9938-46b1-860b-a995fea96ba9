@echo off
chcp 65001
setlocal enabledelayedexpansion
echo ===================================
echo 正在启动服务...
echo ===================================

echo 检查 Java 环境...
where java >nul 2>nul
if %errorlevel% neq 0 (
echo 错误: 未找到 Java，请先安装 Java
pause
exit /b 1
)

echo 检查 Java 版本...
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
set JAVA_VERSION=%%g
)
echo 当前 Java 版本: !JAVA_VERSION!
REM 只取主版本号进行比较
set "JAVA_MAJOR=!JAVA_VERSION:~1,2!"
if !JAVA_MAJOR! LSS 17 (
echo 错误: 需要 Java 17 或更高版本
echo 请安装 Java 17 并设置 JAVA_HOME 环境变量
pause
exit /b 1
)

echo 检查 Node.js 环境...
where node >nul 2>nul
if %errorlevel% neq 0 (
echo 错误: 未找到 Node.js，请先安装 Node.js
pause
exit /b 1
)

echo 检查 Maven 环境...
where mvn >nul 2>nul
if %errorlevel% neq 0 (
echo 错误: 未找到 Maven，请先安装 Maven
pause
exit /b 1
)

echo 创建日志目录...
if not exist logs mkdir logs

echo ===================================
echo 启动 Node.js 服务...
echo ===================================
start cmd /k "chcp 65001 && cd ai-live-master-douyincast-node-service/dycast && echo 正在安装依赖... && npm install && echo 启动 Node.js 服务... && set NODE_OPTIONS=--max_old_space_size=4096 && set PYTHONIOENCODING=utf-8 && set LANG=zh_CN.UTF-8 && npm run dev > ..\..\logs\node-service.log 2>&1"

echo ===================================
echo 构建 Java 项目...
echo ===================================
echo 正在构建所有模块...
set MAVEN_OPTS=-Dfile.encoding=UTF-8
call mvn clean install "-Dmaven.test.skip=true" "-Dfile.encoding=UTF-8"


echo ===================================
echo 启动 Java 服务...
echo ===================================
if not exist "ai-live-master-app\target\ai-live-master-app-1.0-SNAPSHOT.jar" (
echo 错误: 未找到 Java 服务 jar 包
echo 请确保 Maven 构建成功
pause
exit /b 1
)
start "" cmd /c "chcp 65001 && echo 正在启动 Java 服务... && cd ai-live-master-app && set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 && java -jar ./target/ai-live-master-app-1.0-SNAPSHOT.jar --spring.profiles.active=dev > ..\logs\java-service.log 2>&1"

echo ===================================
echo 服务启动中，请稍候...
echo Node.js 服务将运行在: http://localhost:5173
echo Java 服务将运行在: http://localhost:8080/api/v1
echo 日志文件位置:
echo - Node.js 日志: logs\node-service.log
echo - Java 日志: logs\java-service.log
echo ===================================

timeout /t 5
echo 如果服务没有正常启动，请检查日志文件
pause