package com.autoagent.ai_live_master.scriptRecommendation.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.model.PageRequestVO;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptRecommendation.dto.ScriptPlanDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.LiveRoom;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptConfig;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptPlan;
import com.autoagent.ai_live_master.scriptRecommendation.service.ScriptPlanService;
import com.autoagent.ai_live_master.scriptRecommendation.service.ScriptConfigService;
import com.autoagent.ai_live_master.scriptRecommendation.service.LiveRoomService;

import com.autoagent.ai_live_master.scriptRecommendation.service.ScriptRecommendateService;
import com.autoagent.ai_live_master.scriptRecommendation.vo.ScriptPlanVO;
import com.autoagent.ai_live_master.webSocket.service.PuppeteerService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 话术执行计划表 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Tag(name = "12-话术计划管理", description = "话术执行计划的创建、查询和管理相关接口")
@RestController
@RequestMapping("/script-plan")
@RequiredArgsConstructor
@Slf4j
public class ScriptPlanController {

    private final ScriptPlanService scriptPlanService;

    private final ScriptConfigService scriptConfigService;

    private final LiveRoomService liveRoomService;

    private final PuppeteerService puppeteerService;

    private final ScriptRecommendateService scriptRecommendateService;
    /**
     * 创建话术执行计划
     *
     * @param scriptPlanVO 话术执行计划VO
     * @return 创建后的话术执行计划DTO
     */
    @Operation(summary = "创建话术执行计划", description = "创建一个新的话术执行计划")
    @PostMapping
    public ApiResponse<ScriptPlanDTO> createScriptPlan(
            @Parameter(description = "话术执行计划信息", required = true)
            @RequestBody @Valid ScriptPlanVO scriptPlanVO) {
        log.info("创建话术执行计划: {}", scriptPlanVO);
        
        try {
            // 使用BeanConverter转换VO为实体，并设置用户ID
            ScriptPlan scriptPlan = BeanConverter.convert(scriptPlanVO, ScriptPlan.class);
            scriptPlan.setUserId(UserContext.getCurrentUserId());
            
            // 调用服务层创建计划
            ScriptPlanDTO createdPlan = scriptPlanService.createScriptPlan(scriptPlan);
            
            return ApiResponse.success("创建话术执行计划成功", createdPlan);
        } catch (RuntimeException e) {
            log.error("创建话术执行计划失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "创建话术执行计划失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户的所有话术执行计划
     *
     * @return 话术执行计划DTO列表
     */
    @Operation(summary = "获取话术执行计划列表", description = "分页获取当前登录用户创建的所有话术执行计划，支持按计划名称模糊搜索")
    @GetMapping
    public ApiResponse<Page<ScriptPlanDTO>> getScriptPlansByUserId(@ModelAttribute PageRequestVO pageRequest) {
        Long userId = UserContext.getCurrentUserId();
        log.info("获取用户话术执行计划列表 - 用户ID: {}, 页码: {}, 每页大小: {}, 关键字: {}",
                userId, pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());

        try {
            Page<ScriptPlanDTO> scriptPlans = scriptPlanService.getPageByUserId(
                    userId,
                    pageRequest.getPageNum(),
                    pageRequest.getPageSize(),
                    pageRequest.getKeyword());
            log.info("获取用户话术执行计划列表成功, 用户ID: {}, 计划数量: {}", userId, scriptPlans.getTotal());
            return ApiResponse.success(scriptPlans);
        } catch (RuntimeException e) {
            log.error("获取用户话术执行计划列表失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取用户话术执行计划列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新话术执行计划
     *
     * @param id 话术执行计划ID
     * @param scriptPlanVO 话术执行计划VO
     * @return 更新后的话术执行计划DTO
     */
    @Operation(summary = "更新话术执行计划", description = "更新指定ID的话术执行计划")
    @PutMapping("/{id}")
    public ApiResponse<ScriptPlanDTO> updateScriptPlan(
            @Parameter(description = "话术执行计划ID", required = true)
            @PathVariable Integer id,
            @Parameter(description = "话术执行计划信息", required = true)
            @RequestBody @Valid ScriptPlanVO scriptPlanVO) {
        log.info("更新话术执行计划, ID: {}, 数据: {}", id, scriptPlanVO);
        
        try {
            // 使用BeanConverter转换VO为实体，并设置ID和用户ID
            ScriptPlan scriptPlan = BeanConverter.convert(scriptPlanVO, ScriptPlan.class);
            scriptPlan.setId(id);
            scriptPlan.setUserId(UserContext.getCurrentUserId());
            
            // 调用服务层更新计划
            ScriptPlanDTO updatedPlan = scriptPlanService.updateScriptPlan(scriptPlan);
            
            return ApiResponse.success("更新话术执行计划成功", updatedPlan);
        } catch (RuntimeException e) {
            log.error("更新话术执行计划失败: {}", e.getMessage());
            return ApiResponse.error(404, e.getMessage());
        }
    }

    /**
     * 删除话术执行计划
     *
     * @param id 话术执行计划ID
     * @return 无内容响应
     */
    @Operation(summary = "删除话术执行计划", description = "删除指定ID的话术执行计划")
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteScriptPlan(
            @Parameter(description = "话术执行计划ID", required = true)
            @PathVariable Integer id) {
        log.info("删除话术执行计划, ID: {}", id);
        try {
            boolean deleted = scriptPlanService.deleteScriptPlan(id);
            return ApiResponse.success("删除话术执行计划成功", null);
        } catch (RuntimeException e) {
            log.error("删除话术执行计划失败: {}", e.getMessage());
            return ApiResponse.error(404, e.getMessage());
        }
    }

    @Operation(summary = "启动话术执行计划", description = "启动指定ID的话术执行计划，连接对应的直播间")
    @PostMapping("/{id}/start")
    public ApiResponse<String> startScriptPlan(
            @Parameter(description = "话术执行计划ID", required = true)
            @PathVariable Integer id) {
        log.info("启动话术执行计划, ID: {}", id);
        try {
            //TODO  鉴权 检查一下是否属于该登录用户的操作
            // 获取话术执行计划
            ScriptPlan scriptPlan = scriptPlanService.getById(id);
            if (scriptPlan == null) {
                throw new RuntimeException("话术执行计划不存在");
            }
    
            // 获取话术配置
            ScriptConfig scriptConfig = scriptConfigService.getById(scriptPlan.getScriptConfigId());
            if (scriptConfig == null) {
                throw new RuntimeException("话术配置不存在");
            }
    
            // 获取直播间信息
            LiveRoom liveRoom = liveRoomService.getById(scriptConfig.getLiveRoomId());
            if (liveRoom == null) {
                throw new RuntimeException("直播间不存在");
            }
    
            // 连接直播间
//            puppeteerService.connect(String.valueOf(liveRoom.getRoomId()));
            scriptRecommendateService.generateRecommendation(scriptConfig);
            return ApiResponse.success("启动话术执行计划成功", null);
        } catch (Exception e) {
            log.error("启动话术执行计划失败: {}", e.getMessage());
            return ApiResponse.error(500, "启动话术执行计划失败: " + e.getMessage());
        }
    }

    @Operation(summary = "关闭话术执行计划", description = "关闭指定ID的话术执行计划，断开直播间连接")
    @PostMapping("/{id}/stop")
    public ApiResponse<String> stopScriptPlan(
            @Parameter(description = "话术执行计划ID", required = true)
            @PathVariable Integer id) {
        log.info("关闭话术执行计划, ID: {}", id);
        try {
            // 断开直播间连接
            puppeteerService.disconnect();
            return ApiResponse.success("关闭话术执行计划成功", null);
        } catch (Exception e) {
            log.error("关闭话术执行计划失败: {}", e.getMessage());
            return ApiResponse.error(500, "关闭话术执行计划失败: " + e.getMessage());
        }
    }
}