package com.autoagent.ai_live_master.scriptGeneration.service;

import com.autoagent.ai_live_master.scriptGeneration.dto.CompleteScriptsSaveDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CompleteScriptsService OSS功能测试类
 * 主要测试saveScript方法的OSS文件上传功能
 */
@SpringBootTest
@ActiveProfiles("test")
class CompleteScriptsServiceOssTest {

    @Test
    void testCompleteScriptsSaveDTO() {
        // 测试CompleteScriptsSaveDTO的基本功能
        CompleteScriptsSaveDTO dto = new CompleteScriptsSaveDTO();
        dto.setUserId(1L);
        dto.setTitle("测试话术标题");
        dto.setContent("这是一个测试话术内容，用于验证OSS上传功能。\n包含多行文本内容。");
        
        assertEquals(1L, dto.getUserId());
        assertEquals("测试话术标题", dto.getTitle());
        assertTrue(dto.getContent().contains("测试话术内容"));
    }
    
    @Test
    void testFileNameGeneration() {
        // 测试文件名生成逻辑
        Long userId = 123L;
        String title = "测试话术@#$%标题";
        int version = 1;
        
        String expectedFileName = String.format("script_%s_%s_v%d.txt", userId, 
                title.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_"), version);
        
        assertEquals("script_123_测试话术____标题_v1.txt", expectedFileName);
    }
    
    @Test
    void testContentEncoding() {
        // 测试内容编码
        String content = "测试中文内容\n包含换行符和特殊字符：@#$%^&*()";
        
        try {
            byte[] bytes = content.getBytes("UTF-8");
            String decoded = new String(bytes, "UTF-8");
            assertEquals(content, decoded);
        } catch (Exception e) {
            fail("编码测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testVersionIncrement() {
        // 测试版本号递增逻辑
        Integer existingVersion = null;
        int maxVersion1 = existingVersion != null ? existingVersion + 1 : 1;
        assertEquals(1, maxVersion1);
        
        existingVersion = 5;
        int maxVersion2 = existingVersion != null ? existingVersion + 1 : 1;
        assertEquals(6, maxVersion2);
    }
    
    @Test
    void testFilePathValidation() {
        // 测试文件路径验证
        String validOssPath = "https://bucket.oss-region.aliyuncs.com/script_123_test_v1.txt";
        String invalidPath = "";
        
        assertTrue(validOssPath.startsWith("https://"));
        assertTrue(validOssPath.contains(".txt"));
        assertFalse(invalidPath.isEmpty() == false);
    }
}
