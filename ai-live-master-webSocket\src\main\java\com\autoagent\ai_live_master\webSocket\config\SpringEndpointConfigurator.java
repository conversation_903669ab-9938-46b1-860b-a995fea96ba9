package com.autoagent.ai_live_master.webSocket.config;
import com.autoagent.ai_live_master.webSocket.handler.LiveWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import jakarta.websocket.server.ServerEndpointConfig;

@Component
public class SpringEndpointConfigurator extends ServerEndpointConfig.Configurator {

    private static LiveWebSocketHandler liveWebSocketHandler;

    @Autowired
    public void setLiveWebSocketHandler(LiveWebSocketHandler handler) {
        SpringEndpointConfigurator.liveWebSocketHandler = handler;
    }

    @Override
    public <T> T getEndpointInstance(Class<T> endpointClass) throws InstantiationException {
        // 这里返回Spring管理的bean，绕过默认无参构造器创建
        if (endpointClass.equals(LiveWebSocketHandler.class)) {
            return (T) liveWebSocketHandler;
        }
        try {
            return endpointClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new InstantiationException(e.getMessage());
        }
    }
}

