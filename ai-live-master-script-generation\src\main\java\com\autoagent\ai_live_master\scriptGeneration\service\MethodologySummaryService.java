package com.autoagent.ai_live_master.scriptGeneration.service;


import com.autoagent.ai_live_master.scriptGeneration.dto.MethodologySummaryDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologyFile;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologySummary;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface MethodologySummaryService {
    void addSummary(MethodologySummaryDTO dto);

    /**
     * 分页获取当前用户的方法论总结
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 方法论总结分页数据
     */
    Page<MethodologySummary> getPageByUserId(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 根据ID获取方法论总结
     * @param id 方法论总结ID
     * @return 方法论总结
     */
    MethodologySummary getSummaryById(Long id);

    /**
     * 分页获取当前用户的方法论文件上传记录（支持关键字搜索）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 文件记录分页数据
     */
    Page<MethodologyFile> getFilePageByUserId(Long userId, Integer pageNum, Integer pageSize, String keyword);

    /**
     * 根据ID获取方法论文件记录
     * @param id 文件记录ID
     * @return 文件记录
     */
    MethodologyFile getFileById(Long id);
    // 其他业务方法...
}