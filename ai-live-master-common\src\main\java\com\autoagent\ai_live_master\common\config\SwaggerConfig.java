package com.autoagent.ai_live_master.common.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI springShopOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("AI Live Master API")
                        .contact(new Contact())
                        .description("AI Live Master API文档\n\n" +
                                "认证说明：\n" +
                                "1. 首先调用 /auth/login 接口获取token\n" +
                                "2. 点击右上角的 Authorize 按钮\n" +
                                "3. 在弹出框中直接输入token\n" +
                                "4. 点击 Authorize 完成认证")
                        .version("v1")
                        .license(new License().name("Apache 2.0").url("http://springdoc.org")))
                .externalDocs(new ExternalDocumentation()
                        .description("外部文档")
                        .url("https://springshop.wiki.github.org/docs"))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth", 
                            new SecurityScheme()
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .name("token")
                                .description("JWT token，从登录接口获取")))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }
}