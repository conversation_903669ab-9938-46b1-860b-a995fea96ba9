package com.autoagent.ai_live_master.scriptRecommendation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 违禁词库配置实体类
 */
@Data
@TableName(value = "forbidden_terms", autoResultMap = true)
public class ForbiddenTerms {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Long userId;
    
    private String termName;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> terms;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createdAt;
}