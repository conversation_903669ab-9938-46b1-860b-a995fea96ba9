package com.autoagent.ai_live_master.scriptGeneration.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

@Data
public class CompleteScriptsGenerateVO {
    /**
     * 产品描述
     */
    @NotNull(message = "产品信息不能为空")
    private String productDesc;
    
    /**
     * 参考脚本ID列表
     */
    private List<Long> referenceScriptIds;
    
    /**
     * 话术大纲
     */
    @NotBlank(message = "话术大纲不能为空")
    private String scriptOutline;
}