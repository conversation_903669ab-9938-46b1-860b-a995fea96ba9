package com.autoagent.ai_live_master.scriptGeneration.service;


import com.autoagent.ai_live_master.scriptGeneration.dto.ScriptFeaturesDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.ScriptFeatureFile;
import com.autoagent.ai_live_master.scriptGeneration.entity.ScriptFeatures;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface ScriptFeaturesService {
    void addFeature(ScriptFeaturesDTO dto);
    
    /**
     * 分页获取用户的话术特征列表
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 话术特征分页数据
     */
    Page<ScriptFeatures> getPageByUserId(Long userId, Integer pageNum, Integer pageSize);
    
    /**
     * 根据ID获取话术特征
     * @param id 话术特征ID
     * @return 话术特征
     */
    ScriptFeatures getFeatureById(Long id);

    /**
     * 分页获取当前用户的话术特征文件上传记录（支持关键字搜索）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 文件记录分页数据
     */
    Page<ScriptFeatureFile> getFilePageByUserId(Long userId, Integer pageNum, Integer pageSize, String keyword);

    /**
     * 根据ID获取话术特征文件记录
     * @param id 文件记录ID
     * @return 文件记录
     */
    ScriptFeatureFile getFileById(Long id);
}