package com.autoagent.ai_live_master.scriptRecommendation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 直播间基本信息实体类
 */
@Data
@TableName(value = "live_room", autoResultMap = true)
public class LiveRoom {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 新增的字段：直播间房间号 */
    private Long roomId;

    private Long userId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> accountInfo;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> secretKey;

    private String platform;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
