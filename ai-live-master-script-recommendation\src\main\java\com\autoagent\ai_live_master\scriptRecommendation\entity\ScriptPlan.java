package com.autoagent.ai_live_master.scriptRecommendation.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 话术执行计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@TableName("script_plan")
public class ScriptPlan {

    /**
     * 计划唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 计划名称，如"618大促计划"
     */
    private String planName;

    /**
     * 关联的话术配置ID
     */
    private Integer scriptConfigId;

    /**
     * 是否启用计划
     */
    private Boolean isActive;

    /**
     * 计划开始时间
     */
    private LocalDateTime startTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createdAt;


}