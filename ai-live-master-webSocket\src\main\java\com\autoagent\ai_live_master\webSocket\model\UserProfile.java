package com.autoagent.ai_live_master.webSocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfile {
    private String userId;
    private String userName;
    private String userAvatar;
    private Integer userGender;    // 0-未知，1-男，2-女
    private String userShortId;
    private String userDisplayId;
    private String userSecUid;
    private String userWebcastUid;
    private Integer userLevel;
    private List<UserBadge> userBadges;    // 可选：勋章列表
    private FansClubInfo fansClubInfo;    // 可选：粉丝团信息
}