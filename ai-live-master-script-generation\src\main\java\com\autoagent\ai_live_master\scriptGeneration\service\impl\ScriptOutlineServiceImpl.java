package com.autoagent.ai_live_master.scriptGeneration.service.impl;


import com.autoagent.ai_live_master.common.model.Payload;
import com.autoagent.ai_live_master.common.utils.AgentClient;

import com.autoagent.ai_live_master.scriptGeneration.dto.ScriptOutlineDTO;
import com.autoagent.ai_live_master.scriptGeneration.service.ScriptOutlineService;
import com.autoagent.ai_live_master.scriptGeneration.utils.FusionDataBuilder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class ScriptOutlineServiceImpl implements ScriptOutlineService {

    @Autowired
    @Qualifier("outlineAgentClient")
    private AgentClient agentClient;

    @Autowired
    private FusionDataBuilder fusionDataBuilder;

    @Override
    public String generateOutline(ScriptOutlineDTO outlineDTO) {

        // 构建state对象
        Map<String, String> state = fusionDataBuilder.buildFusionState(outlineDTO.getUserId(),outlineDTO.getProductDesc(),outlineDTO.getReferenceScriptIds());
        
        // 构建智能体所需的payload
        Payload payload = Payload.builder()
                .agentId(agentClient.getAgentId())
                .userChatInput("生成话术大纲")
                .state(state)
                .build();
        String generatedOutline;
        try {
            // 日志记录请求参数
            System.out.println("[ScriptOutline] 请求payload: " + payload);
            // 调用智能体同步接口获取处理结果
            generatedOutline = agentClient.chat_completions(payload);
            // 日志记录返回结果
            System.out.println("[ScriptOutline] 智能体返回: " + generatedOutline);
        } catch (Exception e) {
            // 记录异常信息并返回错误提示
            System.err.println("[ScriptOutline] 智能体调用异常: " + e.getMessage());
            throw new RuntimeException("生成话术大纲失败:", e);
        }
        return generatedOutline;
    }
}