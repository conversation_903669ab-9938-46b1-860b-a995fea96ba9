package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.ForbiddenTermsDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ForbiddenTerms;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 违禁词库服务接口
 */
public interface ForbiddenTermsService extends IService<ForbiddenTerms> {
    
    /**
     * 根据ID查询违禁词库
     * @param id 违禁词库ID
     * @return 违禁词库DTO对象
     */
    ForbiddenTermsDTO getForbiddenTermsById(Integer id);

    
    /**
     * 获取所有违禁词库列表
     * @return 违禁词库DTO列表
     */
    List<ForbiddenTermsDTO> getAllForbiddenTerms(Long id);

    /**
     * 分页获取当前用户的违禁词库列表（支持关键字搜索）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 违禁词库分页数据
     */
    Page<ForbiddenTermsDTO> getPageByUserId(Long userId, Integer pageNum, Integer pageSize, String keyword);
    
    /**
     * 创建新违禁词库
     * @param forbiddenTerms 违禁词库实体
     * @return 创建后的违禁词库DTO
     */
    void createForbiddenTerms(ForbiddenTerms forbiddenTerms);
    
    /**
     * 更新违禁词库信息
     * @param forbiddenTerms 违禁词库实体
     * @return 更新后的违禁词库DTO
     */
    ForbiddenTermsDTO updateForbiddenTerms(ForbiddenTerms forbiddenTerms);
    
    /**
     * 删除违禁词库
     * @param id 违禁词库ID
     * @return 是否删除成功
     */
    boolean deleteForbiddenTerms(Integer id);
}