package com.autoagent.ai_live_master.scriptRecommendation.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 违禁词VO，用于接收前端请求数据
 */
@Data
public class ForbiddenTermsVO {

    /**
     * 词库名称，必填
     */
    @NotBlank(message = "词库名称不能为空")
    private String termName;

    /**
     * 违禁词列表，必填，格式为字符串数组
     */
    @NotEmpty(message = "违禁词列表不能为空")
    private List<String> terms;
}
