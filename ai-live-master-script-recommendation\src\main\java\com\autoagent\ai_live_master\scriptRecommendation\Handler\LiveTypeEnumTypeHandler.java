package com.autoagent.ai_live_master.scriptRecommendation.Handler;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.LiveTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

public class LiveTypeEnumTypeHandler extends BaseTypeHandler<LiveTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LiveTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        // 写入数据库时，存中文 label
        ps.setString(i, parameter.getLabel());
    }

    @Override
    public LiveTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String label = rs.getString(columnName);
        if (label == null) {
            return null;
        }
        return LiveTypeEnum.fromLabel(label);
    }

    @Override
    public LiveTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String label = rs.getString(columnIndex);
        if (label == null) {
            return null;
        }
        return LiveTypeEnum.fromLabel(label);
    }

    @Override
    public LiveTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String label = cs.getString(columnIndex);
        if (label == null) {
            return null;
        }
        return LiveTypeEnum.fromLabel(label);
    }
}

