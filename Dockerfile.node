# 使用 Node.js 18 作为基础镜像
FROM node:18-slim

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY ai-live-master-douyincast-node-service/dycast/package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY ai-live-master-douyincast-node-service/dycast/ ./

# 设置环境变量
ENV NODE_ENV=production
ENV NODE_OPTIONS=--max_old_space_size=4096
ENV PYTHONIOENCODING=utf-8
ENV LANG=zh_CN.UTF-8

# 暴露端口
EXPOSE 5173

# 启动命令
CMD ["npm", "run", "dev"] 