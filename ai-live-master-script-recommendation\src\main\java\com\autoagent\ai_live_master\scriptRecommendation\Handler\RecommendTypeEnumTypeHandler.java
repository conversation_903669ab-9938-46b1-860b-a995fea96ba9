package com.autoagent.ai_live_master.scriptRecommendation.Handler;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.RecommendTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

public class RecommendTypeEnumTypeHandler extends BaseTypeHandler<RecommendTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, RecommendTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());  // 存储枚举的 code 到数据库
    }

    @Override
    public RecommendTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int code = rs.getInt(columnName);
        return RecommendTypeEnum.fromCode(code);  // 通过 code 反查枚举
    }

    @Override
    public RecommendTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int code = rs.getInt(columnIndex);
        return RecommendTypeEnum.fromCode(code);
    }

    @Override
    public RecommendTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int code = cs.getInt(columnIndex);
        return RecommendTypeEnum.fromCode(code);
    }
}
