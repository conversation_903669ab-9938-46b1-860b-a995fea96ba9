package com.autoagent.ai_live_master.scriptRecommendation.dto;

import com.autoagent.ai_live_master.scriptRecommendation.entity.LiveRoom;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 直播间数据传输对象
 */
@Data
public class LiveRoomDTO {
    private Integer id;
    private Long roomId;
    private Long userId;
    private Map<String,Object> accountInfo;
    private String platform;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}