package com.autoagent.ai_live_master.scriptRecommendation.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.model.PageRequestVO;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptRecommendation.dto.ForbiddenTermsDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ForbiddenTerms;
import com.autoagent.ai_live_master.scriptRecommendation.service.ForbiddenTermsService;
import com.autoagent.ai_live_master.scriptRecommendation.vo.ForbiddenTermsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 违禁词控制器
 */
@Slf4j
@Tag(name = "11-违禁词管理", description = "违禁词库的创建、查询和管理相关接口")
@RestController
@RequestMapping("/forbidden-terms")
public class ForbiddenTermsController {

    @Autowired
    private ForbiddenTermsService forbiddenTermsService;

    @Operation(summary = "创建违禁词库", description = "创建新的违禁词库")
    @PostMapping
    public ApiResponse<ForbiddenTermsDTO> create(
            @Parameter(description = "违禁词库信息", required = true)
            @Valid @RequestBody ForbiddenTermsVO vo) {
        try {
            ForbiddenTerms entity = BeanConverter.convert(vo, ForbiddenTerms.class);
            entity.setUserId(UserContext.getCurrentUserId());
            forbiddenTermsService.createForbiddenTerms(entity);
            return ApiResponse.success(BeanConverter.convert(entity, ForbiddenTermsDTO.class));
        } catch (Exception e) {
            log.error("创建违禁词库失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "创建违禁词库失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取违禁词库列表", description = "分页获取当前用户的所有违禁词库，支持按词库名称模糊搜索")
    @GetMapping
    public ApiResponse<Page<ForbiddenTermsDTO>> list(@ModelAttribute PageRequestVO pageRequest) {
        try {
            Long currentUserId = UserContext.getCurrentUserId();
            log.info("获取违禁词库列表 - 用户ID: {}, 页码: {}, 每页大小: {}, 关键字: {}",
                    currentUserId, pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());
            Page<ForbiddenTermsDTO> terms = forbiddenTermsService.getPageByUserId(
                    currentUserId,
                    pageRequest.getPageNum(),
                    pageRequest.getPageSize(),
                    pageRequest.getKeyword());
            return ApiResponse.success(terms);
        } catch (Exception e) {
            log.error("获取违禁词库列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取违禁词库列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新违禁词库", description = "更新指定ID的违禁词库信息")
    @PutMapping("/{id}")
    public ApiResponse<ForbiddenTermsDTO> update(
            @Parameter(description = "违禁词库ID", required = true)
            @PathVariable Integer id,
            @Parameter(description = "违禁词库信息", required = true)
            @Valid @RequestBody ForbiddenTermsVO vo) {
        try {
            ForbiddenTerms entity = BeanConverter.convert(vo, ForbiddenTerms.class);
            entity.setId(id);
            entity.setUserId(UserContext.getCurrentUserId());
            return ApiResponse.success(forbiddenTermsService.updateForbiddenTerms(entity));
        } catch (Exception e) {
            log.error("更新违禁词库失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "更新违禁词库失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除违禁词库", description = "删除指定ID的违禁词库")
    @DeleteMapping("/{id}")
    public ApiResponse<Boolean> delete(
            @Parameter(description = "违禁词库ID", required = true)
            @PathVariable Integer id) {
        try {
            return ApiResponse.success(forbiddenTermsService.deleteForbiddenTerms(id));
        } catch (Exception e) {
            log.error("删除违禁词库失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "删除违禁词库失败: " + e.getMessage());
        }
    }
}