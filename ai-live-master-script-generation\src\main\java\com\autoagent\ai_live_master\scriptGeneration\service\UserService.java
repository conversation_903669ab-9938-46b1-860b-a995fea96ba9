package com.autoagent.ai_live_master.scriptGeneration.service;

import com.autoagent.ai_live_master.scriptGeneration.dto.UserDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户DTO对象，不包含敏感信息
     */
    UserDTO getUserById(Integer id);
    
    /**
     * 根据userid查询用户
     * @param userid 用户名
     * @return 用户DTO对象，不包含敏感信息
     */
    UserDTO getUserByUserid(String userid);
    
    /**
     * 获取所有用户列表
     * @return 用户DTO列表，不包含敏感信息
     */
    List<UserDTO> getAllUsers();
    
    /**
     * 分页获取用户列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 用户分页数据
     */
    Page<UserDTO> getPageUsers(Integer pageNum, Integer pageSize);

    /**
     * 分页获取用户列表（支持关键字搜索）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 用户分页数据
     */
    Page<UserDTO> getPageUsers(Integer pageNum, Integer pageSize, String keyword);
    
    /**
     * 创建新用户
     * @param user 用户实体
     * @return 创建后的用户DTO，不包含敏感信息
     */
    UserDTO createUser(User user);
    
    /**
     * 更新用户信息
     * @param user 用户实体
     * @return 更新后的用户DTO，不包含敏感信息
     */
    UserDTO updateUser(User user);
    
    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Integer id);
}