package com.autoagent.ai_live_master.scriptRecommendation.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptRecommendation.dto.LiveRoomDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.LiveRoom;
import com.autoagent.ai_live_master.scriptRecommendation.service.LiveRoomService;
import com.autoagent.ai_live_master.scriptRecommendation.vo.LiveRoomVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播间控制器
 */
@Slf4j
@Tag(name = "08-直播间管理", description = "直播间的创建、查询和管理相关接口")
@RestController
@RequestMapping("/live-rooms")
public class LiveRoomController {

    @Autowired
    private LiveRoomService liveRoomService;

    /**
     * 创建直播间
     * @param liveRoomVO 直播间创建请求
     * @return 创建后的直播间信息
     */
    @Operation(summary = "创建直播间", description = "创建一个新的直播间，包含账号信息、密钥信息和所属平台")
    @PostMapping
    public ApiResponse<String> createLiveRoom(
            @Parameter(description = "直播间创建请求", required = true)
            @Valid @RequestBody LiveRoomVO liveRoomVO) {
        log.info("开始创建直播间 - 请求参数: {}", liveRoomVO);
        try {
            LiveRoom liveRoom = BeanConverter.convert(liveRoomVO, LiveRoom.class);
            liveRoom.setUserId(UserContext.getCurrentUserId());
            liveRoomService.createLiveRoom(liveRoom);
            log.info("直播间创建成功 - liveRoomId: {}", liveRoom.getId());
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("直播间创建失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "创建直播间失败: " + e.getMessage());
        }
    }

    /**
     * 获取直播间列表
     * @return 直播间列表
     */
    @Operation(summary = "获取直播间列表", description = "获取当前用户的所有直播间信息，返回不包含密钥的脱敏结果")
    @GetMapping
    public ApiResponse<List<LiveRoomDTO>> getLiveRooms() {
        log.info("开始获取直播间列表");
        try {
            List<LiveRoomDTO> liveRooms = liveRoomService.getAllLiveRooms(UserContext.getCurrentUserId());
            log.info("获取直播间列表成功 - 数量: {}", liveRooms.size());
            return ApiResponse.success(liveRooms);
        } catch (Exception e) {
            log.error("获取直播间列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取直播间列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新直播间信息
     * @param id 直播间ID
     * @param liveRoom 更新的直播间信息
     * @return 更新后的直播间信息
     */
    @Operation(summary = "更新直播间", description = "根据ID更新指定直播间的信息")
    @PutMapping("/{id}")
    public ApiResponse<LiveRoomDTO> updateLiveRoom(
            @Parameter(description = "直播间ID", required = true)
            @PathVariable Integer id,
            @Parameter(description = "更新的直播间信息", required = true)
            @RequestBody LiveRoom liveRoom) {
        log.info("开始更新直播间 - id: {}, 更新内容: {}", id, liveRoom);
        try {
            liveRoom.setId(id);
            liveRoom.setUserId(UserContext.getCurrentUserId());
            LiveRoomDTO updatedLiveRoom = liveRoomService.updateLiveRoom(liveRoom);
            if (updatedLiveRoom == null) {
                log.warn("更新直播间失败 - 直播间不存在, id: {}", id);
                return ApiResponse.error(404, "直播间不存在");
            }
            log.info("更新直播间成功 - id: {}", id);
            return ApiResponse.success(updatedLiveRoom);
        } catch (Exception e) {
            log.error("更新直播间失败 - id: {}, 错误信息: {}", id, e.getMessage(), e);
            return ApiResponse.error(500, "更新直播间失败: " + e.getMessage());
        }
    }

    /**
     * 删除直播间
     * @param id 直播间ID
     * @return 删除结果
     */
    @Operation(summary = "删除直播间", description = "删除指定ID的直播间")
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteLiveRoom(
            @Parameter(description = "直播间ID", required = true)
            @PathVariable Integer id) {
        log.info("开始删除直播间 - id: {}", id);
        try {
            boolean result = liveRoomService.deleteLiveRoom(id);
            if (!result) {
                log.warn("删除直播间失败 - 直播间不存在, id: {}", id);
                return ApiResponse.error(404, "直播间不存在");
            }
            log.info("删除直播间成功 - id: {}", id);
            return ApiResponse.success("删除成功");
        } catch (Exception e) {
            log.error("删除直播间失败 - id: {}, 错误信息: {}", id, e.getMessage(), e);
            return ApiResponse.error(500, "删除直播间失败: " + e.getMessage());
        }
    }
}