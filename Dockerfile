# 构建阶段
FROM maven:3.8-openjdk-17 AS builder
WORKDIR /app

# 配置Maven使用阿里云镜像
RUN mkdir -p /root/.m2 \
    && echo '<?xml version="1.0" encoding="UTF-8"?>' > /root/.m2/settings.xml \
    && echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"' >> /root/.m2/settings.xml \
    && echo '          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"' >> /root/.m2/settings.xml \
    && echo '          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">' >> /root/.m2/settings.xml \
    && echo '    <mirrors>' >> /root/.m2/settings.xml \
    && echo '        <mirror>' >> /root/.m2/settings.xml \
    && echo '            <id>aliyunmaven</id>' >> /root/.m2/settings.xml \
    && echo '            <mirrorOf>*</mirrorOf>' >> /root/.m2/settings.xml \
    && echo '            <name>阿里云公共仓库</name>' >> /root/.m2/settings.xml \
    && echo '            <url>https://maven.aliyun.com/repository/public</url>' >> /root/.m2/settings.xml \
    && echo '        </mirror>' >> /root/.m2/settings.xml \
    && echo '    </mirrors>' >> /root/.m2/settings.xml \
    && echo '</settings>' >> /root/.m2/settings.xml

COPY pom.xml ./
COPY ai-live-master-app ./ai-live-master-app
COPY ai-live-master-common ./ai-live-master-common
COPY ai-live-master-webSocket ./ai-live-master-webSocket
COPY ai-live-master-script-recommendation ./ai-live-master-script-recommendation
COPY ai-live-master-script-generation ./ai-live-master-script-generation
RUN mvn clean package -DskipTests

FROM openjdk:17-slim
WORKDIR /app

# 更换软件源并安装依赖
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list \
    && sed -i 's/security.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y xvfb libxi6 libgconf-2-4 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装必要的依赖
RUN apt-get update && apt-get install -y \
    xvfb \
    libxi6 \
    libgconf-2-4 \
    unzip \ 
    wget \
    gnupg \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    xdg-utils \
    libcurl3-gnutls \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建 Chrome 和 ChromeDriver 目录
RUN mkdir -p /opt/google/chrome

# 复制本地的 Chrome 和 ChromeDriver
COPY google-chrome-stable_current_amd64.deb /opt/google/chrome/
COPY chromedriver-linux64.zip /opt/google/chrome/

# 安装 Chrome 和 ChromeDriver
RUN cd /opt/google/chrome \
    && dpkg -i google-chrome-stable_current_amd64.deb || true \
    && apt-get install -f -y \
    && unzip chromedriver-linux64.zip \
    && mv chromedriver-linux64/chromedriver /usr/local/bin/ \
    && chmod +x /usr/local/bin/chromedriver \
    && rm -f google-chrome-stable_current_amd64.deb chromedriver-linux64.zip \
    && rm -rf chromedriver-linux64

# 创建日志目录
RUN mkdir -p /app/logs

# 复制构建产物
COPY --from=builder /app/ai-live-master-app/target/ai-live-master-app-1.0-SNAPSHOT.jar /app/

# 设置环境变量
ENV JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF-8"
ENV SPRING_PROFILES_ACTIVE="prod"
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV CHROME_PATH=/usr/bin/google-chrome
ENV CHROME_DRIVER=/usr/local/bin/chromedriver

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["java", "-jar", "ai-live-master-app-1.0-SNAPSHOT.jar"]