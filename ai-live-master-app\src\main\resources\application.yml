spring:
  application:
    name: Ai_live_master
  mvc:
    static-path-pattern: /**
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  datasource:
    url: jdbc:mysql://**************:8306/nlchatdemo
    username: nlchatdemo
    password: nlchatdemo
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      enabled: true
      max-file-size: 10000MB
      max-request-size: 10000MB

server:
  servlet:
    context-path: /api/v1
#    url: ***********************************
#    username: root
#    password: 1234
#    driver-class-name: com.mysql.cj.jdbc.Driver

#控制台输出sql
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# API基础URL
api:
  host: https://uat.agentspro.cn

# 知识库密钥设置
kb:
  auth:
    key: dfb798f07c6c470bb27b6c9f60bae27d
    secret: WskEnEcI7K8yetsxAoMetE9R7a0HBaiH


aliyun:
  # 阿里云OSS配置
  oss:
    endpoint: https://oss-cn-beijing.aliyuncs.com
    bucketName: pefile
    region: cn-beijing
    accessKeyId: LTAI5t6cUTFijqaCZfcpWMH3
    accessKeySecret: ******************************
  # 阿里云语音处理配置
  voice:
    accessKeyId: LTAI5tKh6y5rw5ykoeEFBAPo
    accessKeySecret: ******************************
    appKey: uky3IVNDlLcOVgx9
    regionId: cn-beijing
    endpointName: cn-beijing
    product: nls-filetrans
    domain: filetrans.cn-beijing.aliyuncs.com
    apiVersion: 2018-08-17


#日志记录
logging:
  level:
    com.autoagent.ai_live_master: debug


#JWT令牌的配置信息
jwt:
  utils:
    #密钥
    signKey: autoAgent
    #有效时间
    expire: 43200000

# 智能体配置信息
agent:
  script-agent:
    uuid: 4202b6b216d8414b987b5ed7404cb5cc
    auth-key: 4202b6b216d8414b987b5ed7404cb5cc
    auth-secret: 6UDnHFvol15MfipbNSp4YZyLz4oveM2m
  
  methodology-agent:
    uuid: e49ce3d600c54a78a33fe60bf5ce28cd
    auth-key: e49ce3d600c54a78a33fe60bf5ce28cd
    auth-secret: MwpV3NbIdAuYKflFl0T7D6G9b1rvAKQL
  
  outline-agent:
    uuid: 2a51dbe2f3b140b5bd5ce999bc3eae18
    auth-key: 2a51dbe2f3b140b5bd5ce999bc3eae18
    auth-secret: uA6Iv5R0uTH1ISfm3ljAXNvNQvkxFNwU
  
  fragment-agent:
    uuid: 56216730133042c298a2c3f97054ce1e
    auth-key: 56216730133042c298a2c3f97054ce1e
    auth-secret: Qb7W6Bnp2Ah8ThIkZ3H6vDA649GqLR83
  
  complete-agent:
    uuid: aff5b56b2f4746d5b4aa6b19372426cf
    auth-key: aff5b56b2f4746d5b4aa6b19372426cf
    auth-secret: JMXOVOGdUXWYThCIRom6UcRCaCxL5PGi
  
  product-search-agent:
    uuid: 75f354f0789545eca9a84a54a6494f99
    auth-key: 75f354f0789545eca9a84a54a6494f99
    auth-secret: IIF2GQQal7eYvbL533TrZBsnAQF7CPX6
  
  complete-scripts-extract-agent:
    uuid: 093eca68f40d4870b28c1737155bca0e
    auth-key: 093eca68f40d4870b28c1737155bca0e
    auth-secret: xpiVCPxN5mqjzYU4QABH0XrSeiW8tf23

puppeteer:
#  chrome-path: C:\Program Files\Google\Chrome\Application\chrome.exe
#  chrome-driver: C:\\Users\\<USER>\\Downloads\\chromedriver-win64\\chromedriver-win64\\chromedriver.exe
  ws-base-url: ws://127.0.0.1:8080/api/v1/ws/live/  #推送地址（本地web服务的接口）
  #chrome-path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
  chrome-path: /usr/bin/google-chrome
  chrome-driver: /home/<USER>/chromedriver-linux64/chromedriver


socketio:
  host: "0.0.0.0"               # 监听地址，"0.0.0.0" 表示监听所有网络接口
  port: 3005                    # Socket.IO 服务端口
  boss-count: 1                # boss 线程数，主要用于处理连接请求（一般设置为1即可）
  work-count: 8                # worker 线程数，用于处理消息读写、业务逻辑等（与CPU核心数相关）
  allow-custom-requests: true  # 是否允许自定义 HTTP 请求（如握手请求携带额外参数）
  upgrade-timeout: 10000       # 协议升级超时时间（毫秒），即从 HTTP 升级为 WebSocket 的最大等待时间
  ping-interval: 25000          # 心跳间隔时间（毫秒），服务端向客户端发送 ping 的间隔
  ping-timeout: 60000          # 心跳超时时间（毫秒），客户端在此时间内未响应 ping，则断开连接



