package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.LiveRoomDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.LiveRoom;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 直播间服务接口
 */
public interface LiveRoomService extends IService<LiveRoom> {

    /**
     * 获取所有直播间列表
     * @return 直播间DTO列表，不包含敏感信息
     */
    List<LiveRoomDTO> getAllLiveRooms(Long id);

    /**
     * 分页获取当前用户的直播间列表
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 直播间分页数据，不包含敏感信息
     */
    Page<LiveRoomDTO> getPageByUserId(Long userId, Integer pageNum, Integer pageSize);
    
    /**
     * 创建新直播间
     * @param liveRoom 直播间实体
     * @return 创建后的直播间DTO，不包含敏感信息
     */
    void createLiveRoom(LiveRoom liveRoom);
    
    /**
     * 更新直播间信息
     * @param liveRoom 直播间实体
     * @return 更新后的直播间DTO，不包含敏感信息
     */
    LiveRoomDTO updateLiveRoom(LiveRoom liveRoom);
    
    /**
     * 删除直播间
     * @param id 直播间ID
     * @return 是否删除成功
     */
    boolean deleteLiveRoom(Integer id);
}