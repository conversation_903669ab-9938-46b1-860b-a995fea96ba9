package com.autoagent.ai_live_master.scriptRecommendation.Enum;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum RecommendTypeEnum {

    SYSTEM_RECOMMEND(1, "系统推荐"),
    CUSTOM_PROMPT(2, "自定义提示词"),
    INTELLIGENT_RECOMMEND(3, "智能推荐");

    @EnumValue // MyBatis-Plus 使用此值存数据库
    private final int code;

    @JsonValue // 返回给前端时用这个字段序列化（返回汉字）
    private final String desc;

    RecommendTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 反序列化时前端传中文时转成枚举
    public static RecommendTypeEnum fromDesc(String desc) {
        for (RecommendTypeEnum e : values()) {
            if (e.desc.equals(desc)) {
                return e;
            }
        }
        throw new IllegalArgumentException("未知的推荐类型描述: " + desc);
    }
  // 反序列化时前端传数字时转成枚举

    public static RecommendTypeEnum fromCode(int code) {
        for (RecommendTypeEnum e : values()) {
            if (e.code == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("未知的推荐类型编码: " + code);
    }
}
