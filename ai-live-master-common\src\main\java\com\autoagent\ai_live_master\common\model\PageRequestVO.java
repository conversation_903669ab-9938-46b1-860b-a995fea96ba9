package com.autoagent.ai_live_master.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 分页查询请求参数VO
 * 用于统一处理分页查询的pageNum和pageSize参数
 */
@Data
@Schema(description = "分页查询请求参数")
public class PageRequestVO {
    
    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1", defaultValue = "1")
    @Min(value = 1, message = "页码必须大于0")
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10", defaultValue = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize = 10;

    /**
     * 模糊匹配关键字，可选参数
     * 为空时不进行模糊匹配，不为空时进行模糊匹配
     */
    @Schema(description = "模糊匹配关键字", example = "测试", required = false)
    private String keyword;

    /**
     * 判断是否需要进行模糊匹配
     * @return true表示需要模糊匹配，false表示不需要
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }
}
