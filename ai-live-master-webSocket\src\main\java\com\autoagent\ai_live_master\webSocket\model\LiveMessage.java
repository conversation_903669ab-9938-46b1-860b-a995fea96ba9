package com.autoagent.ai_live_master.webSocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiveMessage {
    private String id;                    // 消息ID
    private String method;                // 消息类型：WebcastChatMessage, WebcastGiftMessage等
    private CastUser user;                // 用户信息
    private CastGift gift;                // 礼物信息
    private String content;               // 消息内容
    private List<CastRtfContent> rtfContent;  // 富文本信息
    private LiveRoom room;                // 房间信息
    private List<LiveRankItem> rank;          // 礼物排行榜信息
    private Long timestamp;               // 时间戳
    private PublicArea publicArea;        // 公共区域信息
    private String eventTime;             // 事件时间
    private Common common;                // 通用信息

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CastUser {
        private String id;                // 用户ID
        private String name;              // 用户昵称
        private String avatar;            // 用户头像
        private Integer gender;           // 性别：0-未知，1-男，2-女
        private String shortId;           // 用户短ID
        private String displayId;         // 用户显示ID
        private String secUid;            // 用户安全ID
        private String webcastUid;        // 用户webcast ID
        private PayGrade payGrade;        // 用户等级信息
        private FansClub fansClub;        // 粉丝团信息
        private FollowInfo followInfo;    // 关注信息
        private BadgeImageList badgeImageList; // 徽章信息
        private Map<String, Object> userAttr; // 用户属性
        private Integer authorizationInfo; // 授权信息
        private Integer mysteryMan;       // 神秘人标识
        private String desensitizedNickname; // 脱敏昵称
    }
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PayGrade {
        private String level;
        private ImageInfo newImIconWithLevel;
        private ImageInfo newLiveIcon;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        private List<String> urlList;
        private String height;
        private String width;
        private Integer imageType;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FansClub {
        private FansClubData data;
        private Map<Integer, FansClubData> preferData;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FansClubData {
        private Integer level;
        private Integer userFansClubStatus;
        private String anchorId;
        private Badge badge;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Badge {
        private Map<String, BadgeIcon> icons;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BadgeIcon {
        private List<String> urlList;
        private String uri;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FollowInfo {
        private String followingCount;
        private String followerCount;
        private String followStatus;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BadgeImageList {
        private List<String> urlList;
        private Integer imageType;
        private BadgeContent content;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BadgeContent {
        private String fontColor;
        private String level;
        private String alternativeText;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CastGift {
        private String id;        // 礼物ID
        private String name;      // 礼物名称
        private Integer price;    // 礼物价值（抖音币）
        private Integer type;     // 礼物类型
        private String desc;      // 礼物描述
        private String icon;      // 礼物图标
        private Object count;     // 礼物数量
        private Integer repeatEnd;// 重复发送标记
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CastRtfContent {
        private Integer type;     // 类型：1-普通文本，2-表情
        private String text;      // 文本内容
        private String url;       // 表情URL
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LiveRoom {
        private Object audienceCount;    // 在线观众数
        private Object likeCount;        // 本场点赞数
        private Object followCount;      // 主播粉丝数
        private Object totalUserCount;   // 累计观看人数
        private Integer status;          // 房间状态
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LiveRankItem {
        private String nickname;  // 用户昵称
        private String avatar;    // 用户头像
        private Object rank;      // 排名
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PublicArea {
        private UserLabel userLabel;
        private String userConsumeInRoom;
        private String userSendGiftCntInRoom;
        private String individualPriority;
        private Integer imAction;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserLabel {
        private List<String> urlList;
        private String avgColor;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Common {
        private String method;
        private String msgId;
        private String roomId;
        private Boolean isShowMsg;
        private String appId;
    }

    public LiveMessage() {
        this.timestamp = System.currentTimeMillis();
        initializeDefaults();
    }

    private void initializeDefaults() {
        // 初始化基本字段
        if (this.id == null) this.id = "";
        if (this.method == null) this.method = "";
        if (this.content == null) this.content = "";
        if (this.eventTime == null) this.eventTime = "";

        // 初始化用户信息
        if (this.user == null) {
            this.user = new CastUser();
            this.user.setId("");
            this.user.setName("");
            this.user.setAvatar("");
            this.user.setShortId("");
            this.user.setDisplayId("");
            this.user.setSecUid("");
            this.user.setWebcastUid("");
            this.user.setDesensitizedNickname("");
        }

        // 初始化礼物信息
        if (this.gift == null) {
            this.gift = new CastGift();
            this.gift.setId("");
            this.gift.setName("");
            this.gift.setDesc("");
            this.gift.setIcon("");
        }

        // 初始化房间信息
        if (this.room == null) {
            this.room = new LiveRoom();
            this.room.setStatus(0);
        }

        // 初始化通用信息
        if (this.common == null) {
            this.common = new Common();
            this.common.setMethod("");
            this.common.setMsgId("");
            this.common.setRoomId("");
            this.common.setAppId("");
        }
    }

    // 创建聊天消息
    public static LiveMessage chat(String id, CastUser user, String content, List<CastRtfContent> rtfContent) {
        LiveMessage message = new LiveMessage();
        message.setId(id);
        message.setMethod("WebcastChatMessage");
        message.setUser(user);
        message.setContent(content);
        message.setRtfContent(rtfContent);
        return message;
    }

    // 创建礼物消息
    public static LiveMessage gift(String id, CastUser user, CastGift gift) {
        LiveMessage message = new LiveMessage();
        message.setId(id);
        message.setMethod("WebcastGiftMessage");
        message.setUser(user);
        message.setGift(gift);
        return message;
    }

    // 创建点赞消息
    public static LiveMessage like(String id, CastUser user, LiveRoom room) {
        LiveMessage message = new LiveMessage();
        message.setId(id);
        message.setMethod("WebcastLikeMessage");
        message.setUser(user);
        message.setRoom(room);
        return message;
    }

    // 创建房间统计消息
    public static LiveMessage roomStats(String id, LiveRoom room) {
        LiveMessage message = new LiveMessage();
        message.setId(id);
        message.setMethod("WebcastRoomStatsMessage");
        message.setRoom(room);
        return message;
    }
} 