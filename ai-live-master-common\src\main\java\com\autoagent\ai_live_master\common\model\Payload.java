package com.autoagent.ai_live_master.common.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class Payload {
    // 代理ID，从AgentClient实例获取
    private String agentId;
    
    // 聊天ID，可选
    private String chatId;
    
    // 用户文本输入，必需
    private String userChatInput;
    
    // 图片输入列表，可选
    private List<ImageInput> images;
    
    // 文件列表，可选
    private List<FileInfo> files;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo {
        // fileId与fileUrl必填其一
        private String fileId;
        // 如果不填会从下载url时获取
        private String fileName;
        // fileId与fileUrl必填其一
        private String fileUrl;
        // 非必填，文件对比时才设置此值
        private String groupName;
    }
    
    // 状态信息，可选
    private Map<String, String> state;
    
    // 按钮标识，可选
    private String buttonKey;
    
    // 调试模式标志，可选
    private Boolean debug;
    
    // 回调URL，可选
    private String callbackUrl;
    
    // 回调头部信息，可选
    private Map<String, String> callbackHeaders;
    
    @Data
    @Builder
    public static class ImageInput {
        private String url;
    }
}