package com.autoagent.ai_live_master.scriptRecommendation.dto;

import com.autoagent.ai_live_master.scriptRecommendation.entity.ForbiddenTerms;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 违禁词库配置DTO
 */
@Data
public class ForbiddenTermsDTO {
    private Integer id;
    private Long userId;
    private String termName;
    private List<String> terms;
    private LocalDateTime createdAt;
}