package com.autoagent.ai_live_master.scriptRecommendation.service.impl;

import com.autoagent.ai_live_master.common.config.ApiConfig;
import com.autoagent.ai_live_master.common.model.Payload;
import com.autoagent.ai_live_master.common.utils.AgentClient;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptConfig;
import com.autoagent.ai_live_master.scriptRecommendation.service.ScriptRecommendateService;
import com.autoagent.ai_live_master.webSocket.handler.SocketMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ScriptRecommendateServiceImpl implements ScriptRecommendateService {

    @Autowired
    private SocketMessageListener socketMessageListener;

    @Override
    public void generateRecommendation(ScriptConfig scriptConfig) {
        AgentClient agentClient=new AgentClient();
        ApiConfig apiConfig=new ApiConfig();
        apiConfig.setHost("https://uat.agentspro.cn");
        agentClient.setApiConfig(apiConfig);
        agentClient.setAgentId("7ceb38e740c44c2d80039143e7119515");
        agentClient.setApiKey("7ceb38e740c44c2d80039143e7119515");
        agentClient.setApiSecret("K7dEysvWX5Dm2NXZ7NDd2I1Mg2ROzW6i");
        agentClient.setListener(socketMessageListener);
        Payload payload= Payload.builder().userChatInput("你好大模型").agentId("7ceb38e740c44c2d80039143e7119515").build();
        try {
            agentClient.chat_completions_streaming(payload);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}