package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptGeneration.dto.MethodologySummaryDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologyFile;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologySummary;
import com.autoagent.ai_live_master.scriptGeneration.service.MethodologySummaryService;
import com.autoagent.ai_live_master.scriptGeneration.vo.MethodologySummaryUploadVO;
import com.autoagent.ai_live_master.scriptGeneration.vo.PageRequestVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "05-方法论总结管理", description = "方法论总结文件的上传和管理相关接口")
@RestController
@RequestMapping("/methodology-summaries")
public class MethodologySummaryController {

    @Autowired
    private MethodologySummaryService methodologySummaryService;

    @Operation(summary = "上传方法论总结文件", description = "上传方法论总结文件并进行处理")
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public ApiResponse<String> add(
            @Parameter(description = "上传文件信息", required = true)
            @ModelAttribute MethodologySummaryUploadVO uploadVO) {
        try {
            log.info("接收到文件上传请求");
            Long currentUserId = UserContext.getCurrentUserId();
            log.info("转换VO为DTO");
            MethodologySummaryDTO dto = BeanConverter.convert(uploadVO, MethodologySummaryDTO.class);
            dto.setUserId(currentUserId);
            log.info("调用服务方法添加方法论");
            methodologySummaryService.addSummary(dto);
            return ApiResponse.success("文件上传成功");
        } catch (Exception e) {
            log.error("添加方法论总结失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "添加方法论总结失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取方法论总结列表", description = "分页获取当前登录用户的所有方法论总结记录")
    @GetMapping("/processed-list")
    public ApiResponse<Page<MethodologySummary>> getSummaryList(@ModelAttribute PageRequestVO pageRequest) {
        try {
            log.info("获取当前用户的方法论总结列表 - 页码: {}, 每页大小: {}", pageRequest.getPageNum(), pageRequest.getPageSize());
            Long currentUserId = UserContext.getCurrentUserId();
            Page<MethodologySummary> summaries = methodologySummaryService.getPageByUserId(currentUserId, pageRequest.getPageNum(), pageRequest.getPageSize());
            return ApiResponse.success(summaries);
        } catch (Exception e) {
            log.error("获取方法论总结列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取方法论总结列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取单个方法论总结", description = "根据方法论总结ID获取详细信息")
    @GetMapping("/processed/{id}")
    public ApiResponse<MethodologySummary> getSummaryById(
            @Parameter(description = "方法论总结ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("根据ID获取方法论总结 - ID: {}", id);
            MethodologySummary summary = methodologySummaryService.getSummaryById(id);
            if (summary == null) {
                return ApiResponse.error(404, "未找到指定的方法论总结");
            }
            return ApiResponse.success(summary);
        } catch (Exception e) {
            log.error("获取方法论总结失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取方法论总结失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取方法论文件上传记录列表", description = "分页获取当前登录用户的所有方法论文件上传记录，支持按文件名模糊搜索")
    @GetMapping("/list")
    public ApiResponse<Page<MethodologyFile>> getFileList(@ModelAttribute PageRequestVO pageRequest) {
        try {
            Long currentUserId = UserContext.getCurrentUserId();
            log.info("获取方法论文件列表 - 用户ID: {}, 页码: {}, 每页大小: {}, 关键字: {}",
                    currentUserId, pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());
            Page<MethodologyFile> files = methodologySummaryService.getFilePageByUserId(
                    currentUserId,
                    pageRequest.getPageNum(),
                    pageRequest.getPageSize(),
                    pageRequest.getKeyword());
            return ApiResponse.success(files);
        } catch (Exception e) {
            log.error("获取方法论文件列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取方法论文件列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取单个方法论文件记录", description = "根据文件记录ID获取详细信息")
    @GetMapping("/{id}")
    public ApiResponse<MethodologyFile> getFileById(
            @Parameter(description = "文件记录ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("根据ID获取方法论文件记录 - ID: {}", id);
            MethodologyFile file = methodologySummaryService.getFileById(id);
            if (file == null) {
                return ApiResponse.error(404, "未找到指定的文件记录");
            }
            return ApiResponse.success(file);
        } catch (Exception e) {
            log.error("获取方法论文件记录失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取方法论文件记录失败: " + e.getMessage());
        }
    }
}
