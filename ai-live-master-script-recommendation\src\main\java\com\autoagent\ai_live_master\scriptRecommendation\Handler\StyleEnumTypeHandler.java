package com.autoagent.ai_live_master.scriptRecommendation.Handler;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.StyleEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

public class StyleEnumTypeHandler extends BaseTypeHandler<StyleEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, StyleEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getLabel());  // 存数据库的中文 label
    }

    @Override
    public StyleEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String label = rs.getString(columnName);
        if (label == null) return null;
        return StyleEnum.fromLabel(label);
    }

    @Override
    public StyleEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String label = rs.getString(columnIndex);
        if (label == null) return null;
        return StyleEnum.fromLabel(label);
    }

    @Override
    public StyleEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String label = cs.getString(columnIndex);
        if (label == null) return null;
        return StyleEnum.fromLabel(label);
    }
}

