package com.autoagent.ai_live_master.scriptGeneration.service.impl;


import com.autoagent.ai_live_master.common.utils.JwtUtils;
import com.autoagent.ai_live_master.scriptGeneration.entity.User;
import com.autoagent.ai_live_master.scriptGeneration.mapper.UserMapper;


import com.autoagent.ai_live_master.scriptGeneration.service.AuthService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtils jwtUtils;

    @Override
    public String authenticate(String username, String password) throws Exception {
        // 使用 QueryWrapper 构造查询条件
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("userid", username);

        // 查询数据库验证用户名和密码
        User user = userMapper.selectOne(queryWrapper);
        if (user != null && user.getUserPassword().equals(password)) {
            // 生成JWT令牌
            Map<String,Object> claims=new HashMap<>();
            claims.put("id",user.getId());
            claims.put("username",user.getUserid());
            return jwtUtils.generateJwt(claims);
        } else {
            return null; // 返回null表示认证失败
        }
    }
}