package com.autoagent.ai_live_master.scriptRecommendation.Enum;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
public enum LiveTypeEnum {
    SINGLE("单品"),
    MULTI("多品");

    @JsonValue
    @EnumValue
    private final String label;

    LiveTypeEnum(String label) {
        this.label = label;
    }

    // 根据数据库中的中文label获取枚举
    public static LiveTypeEnum fromLabel(String label) {
        for (LiveTypeEnum type : values()) {
            if (type.label.equals(label)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的直播类型: " + label);
    }
}

