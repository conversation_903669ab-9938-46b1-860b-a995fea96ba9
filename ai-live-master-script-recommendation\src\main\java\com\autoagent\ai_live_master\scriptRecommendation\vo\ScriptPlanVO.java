package com.autoagent.ai_live_master.scriptRecommendation.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ScriptPlanVO {

    /**
     * 计划名称，如"618大促计划"
     */
    @NotBlank(message = "计划名称不能为空")
    private String planName;

    /**
     * 关联的话术配置ID
     */
    @NotNull(message = "话术配置ID不能为空")
    private Integer scriptConfigId;

    /**
     * 是否启用计划
     */
    private Boolean isActive = true;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 计划结束时间
     */
    @NotNull(message = "计划结束时间不能为空")
    private LocalDateTime endTime;
}
