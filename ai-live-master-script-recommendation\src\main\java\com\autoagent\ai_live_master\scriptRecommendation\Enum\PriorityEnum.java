package com.autoagent.ai_live_master.scriptRecommendation.Enum;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PriorityEnum {
    DATA_FIRST("异动数据优先"),
    SCRIPT_FIRST("推荐话术优先");

    @JsonValue
    @EnumValue
    private final String label;


    public static PriorityEnum fromLabel(String label) {
        for (PriorityEnum p : values()) {
            if (p.label.equals(label)) return p;
        }
        throw new IllegalArgumentException("无效的优先级: " + label);
    }
}
