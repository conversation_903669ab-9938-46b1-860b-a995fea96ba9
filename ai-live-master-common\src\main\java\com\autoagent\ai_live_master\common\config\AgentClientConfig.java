package com.autoagent.ai_live_master.common.config;


import com.autoagent.ai_live_master.common.utils.AgentClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AgentClientConfig {
    
    @Autowired
    private AgentConfig agentConfig;
    
    @Bean("scriptAgentClient")
    public AgentClient scriptAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getScriptAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getScriptAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getScriptAgent().getUuid());
        return agentClient;
    }
    
    @Bean("methodologyAgentClient")
    public AgentClient methodologyAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getMethodologyAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getMethodologyAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getMethodologyAgent().getUuid());
        return agentClient;
    }
    
    @Bean("outlineAgentClient")
    public AgentClient outlineAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getOutlineAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getOutlineAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getOutlineAgent().getUuid());
        return agentClient;
    }
    
    @Bean("fragmentAgentClient")
    public AgentClient fragmentAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getFragmentAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getFragmentAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getFragmentAgent().getUuid());
        return agentClient;
    }
    
    @Bean("completeAgentClient")
    public AgentClient completeAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getCompleteAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getCompleteAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getCompleteAgent().getUuid());
        return agentClient;
    }
    
    @Bean("productSearchAgentClient")
    public AgentClient productSearchAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getProductSearchAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getProductSearchAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getProductSearchAgent().getUuid());
        return agentClient;
    }
    
    @Bean("completeScriptsExtractAgentClient")
    public AgentClient completeScriptsExtractAgentClient() {
        AgentClient agentClient = new AgentClient();
        agentClient.setApiKey(agentConfig.getCompleteScriptsExtractAgent().getAuthKey());
        agentClient.setApiSecret(agentConfig.getCompleteScriptsExtractAgent().getAuthSecret());
        agentClient.setAgentId(agentConfig.getCompleteScriptsExtractAgent().getUuid());
        return agentClient;
    }
}