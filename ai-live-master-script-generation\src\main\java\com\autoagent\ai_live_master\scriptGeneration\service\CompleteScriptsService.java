package com.autoagent.ai_live_master.scriptGeneration.service;


import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.scriptGeneration.dto.CompleteScriptsGenerateDTO;
import com.autoagent.ai_live_master.scriptGeneration.dto.CompleteScriptsSaveDTO;
import com.autoagent.ai_live_master.scriptGeneration.dto.FileUploadDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.CompleteScripts;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public interface CompleteScriptsService {
    
    /**
     * 添加完整话术
     */
    void addScript(FileUploadDTO dto);

    /**
     * 根据用户id分页获取完整话术列表
     */
    ApiResponse<Page<CompleteScripts>> getScriptsByUserId(Long userId, Integer pageNum, Integer pageSize);

    String generateCompleteScript(CompleteScriptsGenerateDTO dto);
    
    /**
     * 保存生成的话术内容
     */
    void saveScript(CompleteScriptsSaveDTO dto);

    /**
     * 根据ID获取话术内容
     * @param id 话术ID
     * @return 话术内容
     */
    CompleteScripts getScriptById(Long id);

    /**
     * 更新话术内容
     * @param dto 话术更新DTO
     * @return 更新后的话术内容
     */
    CompleteScripts updateScript(CompleteScriptsSaveDTO dto);

    /**
     * 删除话术
     * @param id 话术ID
     */
    void deleteScript(Long id);
}