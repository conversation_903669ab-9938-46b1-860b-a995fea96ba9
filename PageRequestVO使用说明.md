# PageRequestVO 使用说明

## 概述

`PageRequestVO` 是一个通用的分页查询请求参数VO类，用于统一处理分页查询的 `pageNum`、`pageSize` 和 `keyword` 参数。

## 功能特性

1. **分页参数**：支持页码和每页大小的设置
2. **模糊匹配**：支持可选的关键字模糊匹配功能
3. **参数验证**：内置参数验证注解，确保数据有效性
4. **工具方法**：提供 `hasKeyword()` 方法判断是否需要进行模糊匹配

## 字段说明

| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNum | Integer | 是 | 1 | 页码，从1开始 |
| pageSize | Integer | 是 | 10 | 每页大小 |
| keyword | String | 否 | null | 模糊匹配关键字 |

## 使用方式

### 1. Controller层使用

```java
@Operation(summary = "获取用户列表", description = "分页获取所有用户信息，支持关键字搜索")
@GetMapping
public ApiResponse<Page<UserDTO>> list(@ModelAttribute PageRequestVO pageRequest) {
    try {
        log.info("获取用户列表 - 页码: {}, 每页大小: {}, 关键字: {}", 
                pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());
        Page<UserDTO> users = userService.getPageUsers(
                pageRequest.getPageNum(), 
                pageRequest.getPageSize(), 
                pageRequest.getKeyword());
        return ApiResponse.success(users);
    } catch (Exception e) {
        log.error("获取用户列表失败 - 错误信息: {}", e.getMessage(), e);
        return ApiResponse.error(500, "获取用户列表失败: " + e.getMessage());
    }
}
```

### 2. Service层使用

```java
@Override
public Page<UserDTO> getPageUsers(Integer pageNum, Integer pageSize, String keyword) {
    Page<User> page = new Page<>(pageNum, pageSize);
    
    // 构建查询条件
    LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
    if (keyword != null && !keyword.trim().isEmpty()) {
        // 对用户名进行模糊匹配
        queryWrapper.like(User::getUserid, keyword.trim());
    }
    
    Page<User> userPage = this.page(page, queryWrapper);
    
    // 转换为DTO
    Page<UserDTO> dtoPage = new Page<>();
    dtoPage.setTotal(userPage.getTotal());
    dtoPage.setCurrent(userPage.getCurrent());
    dtoPage.setSize(userPage.getSize());
    dtoPage.setRecords(userPage.getRecords().stream()
            .map(UserDTO::fromEntity)
            .collect(Collectors.toList()));
    return dtoPage;
}
```

### 3. 使用工具方法

```java
PageRequestVO pageRequest = new PageRequestVO();
pageRequest.setKeyword("测试");

// 判断是否需要进行模糊匹配
if (pageRequest.hasKeyword()) {
    // 执行模糊匹配逻辑
    queryWrapper.like(Entity::getField, pageRequest.getKeyword().trim());
}
```

## API 请求示例

### 基本分页查询
```
GET /users?pageNum=1&pageSize=10
```

### 带关键字的分页查询
```
GET /users?pageNum=1&pageSize=10&keyword=admin
```

### 只使用关键字（使用默认分页参数）
```
GET /users?keyword=test
```

## 已应用的Controller

以下Controller已经使用了 `PageRequestVO`：

1. **UserController** - 用户列表查询
2. **MethodologySummaryController** - 方法论总结列表查询
3. **ScriptFeaturesController** - 话术特征列表查询
4. **CompleteScriptsController** - 完整话术列表查询

## 扩展建议

如果需要为其他Controller添加模糊匹配功能，可以：

1. 在对应的Service接口中添加支持keyword参数的方法
2. 在ServiceImpl中实现具体的模糊匹配逻辑
3. 修改Controller方法使用PageRequestVO参数
4. 根据实体字段特点，在查询条件中添加适当的模糊匹配字段

## 注意事项

1. `keyword` 参数是可选的，为空时不会进行模糊匹配
2. 使用 `@ModelAttribute` 注解来接收GET请求的查询参数
3. 建议在Service层使用 `hasKeyword()` 方法来判断是否需要添加模糊匹配条件
4. 模糊匹配的具体字段需要根据实际业务需求在Service层实现
