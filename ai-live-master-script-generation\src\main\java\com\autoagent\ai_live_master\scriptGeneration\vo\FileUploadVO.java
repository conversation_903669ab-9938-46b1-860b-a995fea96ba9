package com.autoagent.ai_live_master.scriptGeneration.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传VO类
 * 用于接收前端上传的文件和标题
 */
@Data
public class FileUploadVO {
    /**
     * 上传的文件
     */
    @NotBlank(message = "文件不能为空")
    private MultipartFile file;
    
    /**
     * 文件标题
     */
//    @NotBlank(message = "话术标题不能为空")
    private String title;
}