package com.autoagent.ai_live_master.scriptRecommendation.dto;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.LiveTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.PriorityEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.RecommendTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.StyleEnum;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptConfig;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.EnumTypeHandler;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <p>
 * 话术生成核心配置表 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class ScriptConfigDTO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Long userId;

    private Integer liveRoomId;

    private String configName;

    private Integer completeScript;

    @TableField(typeHandler = EnumTypeHandler.class)
    private LiveTypeEnum liveType;

    private String productInfo;

    @TableField(typeHandler = EnumTypeHandler.class)
    private StyleEnum style;

    private Boolean exaggeration;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private ScriptConfig.DataAlertConfig dataAlerts;

    @TableField(typeHandler = EnumTypeHandler.class)
    private PriorityEnum priority;

    @TableField(typeHandler = EnumTypeHandler.class)
    private RecommendTypeEnum recommendType;

    private Integer scriptConfigForbidden;

    private Integer scriptConfigBenefit;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private ScriptConfig.ShieldConfig shieldConfig;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private ScriptConfig.TargetAudience targetAudience;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createdAt;
}