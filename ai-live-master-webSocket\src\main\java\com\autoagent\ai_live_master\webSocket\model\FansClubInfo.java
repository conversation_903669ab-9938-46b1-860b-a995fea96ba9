package com.autoagent.ai_live_master.webSocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FansClubInfo {
    private Integer level;
    private Integer userFansClubStatus;
    private String anchorId;
    private Badge badge;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Badge {
        private Map<String, BadgeIcon> icons;
        
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class BadgeIcon {
            private List<String> urlList;
            private String uri;
        }
    }
}