package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.EcommerceBenefitsDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.EcommerceBenefits;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 电商权益配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface EcommerceBenefitsService extends IService<EcommerceBenefits> {

    /**
     * 根据用户ID查询电商权益配置列表
     * @param userId 用户ID
     * @return 电商权益配置DTO列表
     */
    List<EcommerceBenefitsDTO> getEcommerceBenefitsByUserId(Long userId);



    /**
     * 创建新电商权益配置
     * @param ecommerceBenefits 电商权益配置DTO
     * @return 创建后的电商权益配置DTO
     */
    EcommerceBenefitsDTO createEcommerceBenefits(EcommerceBenefits ecommerceBenefits);

    /**
     * 更新电商权益配置信息
     * @param ecommerceBenefits 电商权益配置DTO
     * @return 更新后的电商权益配置DTO
     */
    EcommerceBenefitsDTO updateEcommerceBenefits(EcommerceBenefits ecommerceBenefits);

    /**
     * 删除电商权益配置
     * @param id 权益配置ID
     * @return 是否删除成功
     */
    boolean deleteEcommerceBenefits(Integer id);
}