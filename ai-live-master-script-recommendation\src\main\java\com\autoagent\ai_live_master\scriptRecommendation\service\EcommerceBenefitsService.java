package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.EcommerceBenefitsDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.EcommerceBenefits;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 电商权益配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface EcommerceBenefitsService extends IService<EcommerceBenefits> {

    /**
     * 根据用户ID查询电商权益配置列表
     * @param userId 用户ID
     * @return 电商权益配置DTO列表
     */
    List<EcommerceBenefitsDTO> getEcommerceBenefitsByUserId(Long userId);

    /**
     * 分页获取当前用户的电商权益配置列表（支持关键字搜索）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 电商权益配置分页数据
     */
    Page<EcommerceBenefitsDTO> getPageByUserId(Long userId, Integer pageNum, Integer pageSize, String keyword);



    /**
     * 创建新电商权益配置
     * @param ecommerceBenefits 电商权益配置DTO
     * @return 创建后的电商权益配置DTO
     */
    EcommerceBenefitsDTO createEcommerceBenefits(EcommerceBenefits ecommerceBenefits);

    /**
     * 更新电商权益配置信息
     * @param ecommerceBenefits 电商权益配置DTO
     * @return 更新后的电商权益配置DTO
     */
    EcommerceBenefitsDTO updateEcommerceBenefits(EcommerceBenefits ecommerceBenefits);

    /**
     * 删除电商权益配置
     * @param id 权益配置ID
     * @return 是否删除成功
     */
    boolean deleteEcommerceBenefits(Integer id);
}