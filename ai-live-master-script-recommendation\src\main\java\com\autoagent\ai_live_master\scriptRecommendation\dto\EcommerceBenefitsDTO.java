package com.autoagent.ai_live_master.scriptRecommendation.dto;

import com.autoagent.ai_live_master.scriptRecommendation.entity.EcommerceBenefits;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;

/**
 * <p>
 * 电商权益配置表 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class EcommerceBenefitsDTO {

    private Integer id;

    private Long userId;

    private String benefitName;

    private Map<String, Object> benefits;

    private Timestamp createdAt;

}