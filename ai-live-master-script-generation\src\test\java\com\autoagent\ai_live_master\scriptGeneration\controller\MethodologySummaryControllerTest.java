package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologyFile;
import com.autoagent.ai_live_master.scriptGeneration.vo.PageRequestVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MethodologySummaryController 测试类
 * 主要测试新增的文件查询接口
 */
@SpringBootTest
@ActiveProfiles("test")
class MethodologySummaryControllerTest {

    @Test
    void testPageRequestVO() {
        // 测试PageRequestVO的基本功能
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageNum(1);
        pageRequest.setPageSize(10);
        pageRequest.setKeyword("方法论");
        
        assertEquals(1, pageRequest.getPageNum());
        assertEquals(10, pageRequest.getPageSize());
        assertEquals("方法论", pageRequest.getKeyword());
        assertTrue(pageRequest.hasKeyword());
    }
    
    @Test
    void testMethodologyFileEntity() {
        // 测试MethodologyFile实体类
        MethodologyFile file = new MethodologyFile();
        file.setId(1L);
        file.setUserId(123L);
        file.setFileName("方法论总结.pdf");
        file.setFilePath("https://bucket.oss-region.aliyuncs.com/uuid.pdf");
        file.setRemarks("方法论总结文件");
        
        assertEquals(1L, file.getId());
        assertEquals(123L, file.getUserId());
        assertEquals("方法论总结.pdf", file.getFileName());
        assertEquals("https://bucket.oss-region.aliyuncs.com/uuid.pdf", file.getFilePath());
        assertEquals("方法论总结文件", file.getRemarks());
    }
    
    @Test
    void testApiPaths() {
        // 测试API路径
        String listPath = "/methodology-summaries/list";
        String detailPath = "/methodology-summaries/list/{id}";
        
        assertTrue(listPath.startsWith("/methodology-summaries"));
        assertTrue(detailPath.contains("{id}"));
    }
    
    @Test
    void testKeywordSearch() {
        // 测试关键字搜索逻辑
        String keyword1 = null;
        String keyword2 = "";
        String keyword3 = "   ";
        String keyword4 = "方法论";
        
        assertFalse(hasKeyword(keyword1));
        assertFalse(hasKeyword(keyword2));
        assertFalse(hasKeyword(keyword3));
        assertTrue(hasKeyword(keyword4));
    }
    
    private boolean hasKeyword(String keyword) {
        return keyword != null && !keyword.trim().isEmpty();
    }
}
