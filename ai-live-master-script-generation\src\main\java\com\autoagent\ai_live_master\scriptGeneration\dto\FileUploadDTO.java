package com.autoagent.ai_live_master.scriptGeneration.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传DTO类
 * 用于将前端上传的文件信息传递给服务层
 */
@Data
public class FileUploadDTO {
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 文件
     */
    private MultipartFile file;
    
    /**
     * 文件标题
     */
    private String title;

    /**
     * 文件内容的字节数组
     * 用于在异步处理时保存文件内容，避免访问已被删除的临时文件
     */
    private byte[] fileContent;
}