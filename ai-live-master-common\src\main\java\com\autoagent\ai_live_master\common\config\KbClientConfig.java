package com.autoagent.ai_live_master.common.config;

import com.autoagent.ai_live_master.common.utils.KbClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KbClientConfig {
    
    @Autowired
    private KbAuthConfig kbAuthConfig;
    
    @Bean
    public KbClient kbClient() {
        KbClient kbClient = new KbClient();
        kbClient.setAuthKey(kbAuthConfig.getKey());
        kbClient.setAuthSecret(kbAuthConfig.getSecret());
        return kbClient;
    }
}