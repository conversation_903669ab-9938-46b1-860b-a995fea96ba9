package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统健康状态检查接口
 */
@Slf4j
@Tag(name = "01-系统健康检查", description = "系统健康状态检查相关接口")
@RestController
public class HealthController {

    /**
     * 健康检查接口
     * 该接口不被拦截器拦截，可以直接访问
     * @return 系统健康状态信息
     */
    @Operation(summary = "检查系统健康状态", description = "返回系统当前的健康状态信息")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> healthCheck() {
        log.info("健康检查接口被调用");
        
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("application", "ai_live_master");
        healthInfo.put("version", "1.0.0");
        healthInfo.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success(healthInfo);
    }
}