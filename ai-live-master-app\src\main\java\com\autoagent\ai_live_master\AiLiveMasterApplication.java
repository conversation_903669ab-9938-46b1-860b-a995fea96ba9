package com.autoagent.ai_live_master;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


@SpringBootApplication(scanBasePackages = {"com.autoagent.ai_live_master"})
@MapperScan("com.autoagent.ai_live_master.**.mapper")
@EnableAsync
@EnableScheduling
public class AiLiveMasterApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiLiveMasterApplication.class, args);
        System.out.println("\n\nSwagger UI 访问地址: http://localhost:8080/api/v1/swagger-ui/index.html\n");
    }

}