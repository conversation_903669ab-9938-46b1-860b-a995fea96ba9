<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.autoagent</groupId>
        <artifactId>ai-live-master</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ai-live-master-script-recommendation</artifactId>

    <dependencies>
        <!-- 依赖公共模块 -->
        <dependency>
            <groupId>com.autoagent</groupId>
            <artifactId>ai-live-master-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.autoagent</groupId>
            <artifactId>ai-live-master-webSocket</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <phase>none</phase> <!-- 禁用 repackage -->
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>