package com.autoagent.ai_live_master.scriptRecommendation.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 直播间创建请求VO
 */
@Data
public class LiveRoomVO {

    /**
     * 账号信息，必须是 JSON 对象
     */
    /** 新增的字段：直播间房间号 */
    private Long roomId;

    @NotNull(message = "账号信息不能为空")
    private Map<String, Object> accountInfo;

    /**
     * 密钥信息，必须是 JSON 对象
     */
    private Map<String, Object> secretKey;

    /**
     * 直播平台
     */
    @NotNull(message = "平台信息不能为空")
    private String platform;
}
