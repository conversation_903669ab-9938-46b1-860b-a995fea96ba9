package com.autoagent.ai_live_master.scriptGeneration.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class ScriptFragmentsVO {
    /**
     * 产品描述
     */
    @NotNull(message = "产品描述不能为空")
    private String productDesc;
    
    /**
     * 参考脚本ID列表
     */
    private List<Long> referenceScriptIds;
    
    /**
     * 话术大纲
     */
    @NotBlank(message = "话术大纲不能为空")
    private String scriptOutline;
    
    /**
     * 当前选择的文本
     */
    @NotBlank(message = "当前选择的文本不能为空")
    private String currentSelection;
    
    /**
     * 当前内容
     */
    @NotBlank(message = "当前全文内容")
    private String currentContent;
    
    /**
     * 用户生成的特殊要求
     */
    private String userGeneratedRequest;
}