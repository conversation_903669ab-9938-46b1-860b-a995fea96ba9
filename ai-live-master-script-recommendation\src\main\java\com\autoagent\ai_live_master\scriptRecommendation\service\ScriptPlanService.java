package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.ScriptPlanDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptPlan;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 话术执行计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
public interface ScriptPlanService extends IService<ScriptPlan> {


    /**
     * 获取所有话术执行计划列表
     * @return 话术执行计划DTO列表
     */
    List<ScriptPlanDTO> getAllScriptPlans();

    /**
     * 创建新话术执行计划
     * @param scriptPlan 话术执行计划
     * @return 创建后的话术执行计划DTO
     */
    ScriptPlanDTO createScriptPlan(ScriptPlan scriptPlan);

    /**
     * 更新话术执行计划信息
     * @param scriptPlan 话术执行计划
     * @return 更新后的话术执行计划DTO
     */
    ScriptPlanDTO updateScriptPlan(ScriptPlan scriptPlan);

    /**
     * 删除话术执行计划
     * @param id 计划ID
     * @return 是否删除成功
     */
    boolean deleteScriptPlan(Integer id);
}