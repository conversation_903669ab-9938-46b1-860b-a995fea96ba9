package com.autoagent.ai_live_master.webSocket.service;

import com.autoagent.ai_live_master.webSocket.handler.SocketIOHandler;
import com.autoagent.ai_live_master.webSocket.model.WordCloudEntry;
import com.autoagent.ai_live_master.webSocket.model.WordStats;
import com.autoagent.ai_live_master.webSocket.vo.BulletScreenWordCloudMessage;
import com.autoagent.ai_live_master.webSocket.vo.FormulaicScriptMessage;
import com.autoagent.ai_live_master.webSocket.vo.RecommendedScriptMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class WordCloudService {

    private final JiebaSegmenter segmenter = new JiebaSegmenter();

    // 词频统计表（线程安全）
    // 房间ID -> (词语 -> (词频，更新时间))
    // 房间ID -> 词频映射
    private final Map<String, Map<String, WordStats>> roomWordFreqMap = new ConcurrentHashMap<>();

    // 停用词（可自定义）
    private final Set<String> stopWords = Set.of("的", "了", "是", "在", "我", "你", "我们", "他们", "就");

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // 词云淘汰阈值（毫秒）
    private static final long WORD_EXPIRATION_TIME_MS = 5 * 60 * 1000; // 5分钟

    // 每次推送最多的词数量
    private static final int MAX_WORD_COUNT = 50;

    @Autowired
    private SocketIOHandler socketIOHandler;

    // 每条弹幕进来后调用此方法进行分词统计
    public void addMessage(String roomId, String content) {
        String cleanContent = stripHtmlTags(content);
        List<SegToken> tokens = segmenter.process(cleanContent, JiebaSegmenter.SegMode.SEARCH);
        Map<String, WordStats> wordFreqMap = roomWordFreqMap.computeIfAbsent(roomId, k -> new ConcurrentHashMap<>());

        for (SegToken token : tokens) {
            String word = token.word;
            if (!stopWords.contains(word) && word.length() > 1) {
                wordFreqMap.compute(word, (k, v) -> {
                    if (v == null) return new WordStats(1, System.currentTimeMillis());
                    v.increment();
                    return v;
                });
            }
        }
    }

    // 定期（比如每 30 秒）获取词云数据并清空旧数据
    @Scheduled(fixedRate = 30000)
    public void generateAndPushWordCloud() throws JsonProcessingException {

        for (String roomId : roomWordFreqMap.keySet()) {
            Map<String, WordStats> wordFreqMap = roomWordFreqMap.get(roomId);
            cleanupOldWords(wordFreqMap);

            List<WordCloudEntry> wordCloudList = wordFreqMap.entrySet().stream()
                    .sorted((a, b) -> b.getValue().getCount() - a.getValue().getCount())
                    .limit(MAX_WORD_COUNT)
                    .map(e -> new WordCloudEntry(e.getKey(), e.getValue().getCount()))
                    .collect(Collectors.toList());

            // 构建词云消息体
            BulletScreenWordCloudMessage wordCloudMessage = new BulletScreenWordCloudMessage(wordCloudList);
            String wordCloudJson = OBJECT_MAPPER.writeValueAsString(wordCloudMessage);
            //构建定时话术
            FormulaicScriptMessage formulaic = new FormulaicScriptMessage(
                    "欢迎来到我们的直播间！今天为大家带来超值新品～",
                    "abc123456789"
            );
            //构建推荐话术
            RecommendedScriptMessage recommended = new RecommendedScriptMessage(
                    "这款产品主打性价比，限时优惠，现在下单享七折！",
                    "产品推荐,优惠,性价比",
                    true,
                    "rec987654321"
            );
            String formulaicJson = OBJECT_MAPPER.writeValueAsString(formulaic);
            String recommendedJson= OBJECT_MAPPER.writeValueAsString(recommended);
            // 广播词云消息
            socketIOHandler.broadcast(roomId,wordCloudJson);
            socketIOHandler.broadcast(roomId,formulaicJson);
            socketIOHandler.broadcast(roomId,recommendedJson);
        }
    }

    /**
     * 清理词频表中过期的词
     */
    private void cleanupOldWords(Map<String, WordStats> wordFreqMap) {
        long now = System.currentTimeMillis();
        wordFreqMap.entrySet().removeIf(e -> (now - e.getValue().getLastUpdateTime()) > WORD_EXPIRATION_TIME_MS);
    }

    private String stripHtmlTags(String html) {
        return html.replaceAll("<[^>]*>", ""); // 清除所有 HTML 标签
    }

    public void resetWordFreqData() {
        roomWordFreqMap.clear();
    }

}

