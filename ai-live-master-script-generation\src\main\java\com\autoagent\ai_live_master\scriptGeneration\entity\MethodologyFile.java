package com.autoagent.ai_live_master.scriptGeneration.entity;

import com.autoagent.ai_live_master.scriptGeneration.Enum.FileStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("methodology_files")
public class MethodologyFile {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String fileName;

    private String filePath;

    private FileStatusEnum status;

    private String remarks;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime uploadTime;
}
