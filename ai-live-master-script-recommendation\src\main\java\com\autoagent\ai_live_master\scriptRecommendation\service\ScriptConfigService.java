package com.autoagent.ai_live_master.scriptRecommendation.service;

import com.autoagent.ai_live_master.scriptRecommendation.dto.ScriptConfigDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 话术生成核心配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface ScriptConfigService extends IService<ScriptConfig> {

    /**
     * 获取所有话术配置列表
     * @return 话术配置DTO列表
     * @throws RuntimeException 查询过程中发生异常
     */
    List<ScriptConfigDTO> getAllScriptConfigs();

    /**
     * 分页获取当前用户的话术配置列表（支持关键字搜索）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键字，为空时不进行模糊匹配
     * @return 话术配置分页数据
     */
    Page<ScriptConfigDTO> getPageByUserId(Long userId, Integer pageNum, Integer pageSize, String keyword);

    /**
     * 创建新话术配置
     * @param scriptConfig 话术配置
     * @return 创建后的话术配置DTO
     * @throws RuntimeException 创建过程中发生异常
     */
    ScriptConfigDTO createScriptConfig(ScriptConfig scriptConfig);

    /**
     * 更新话术配置信息
     * @param scriptConfig 话术配置
     * @return 更新后的话术配置DTO
     * @throws RuntimeException 更新过程中发生异常或者记录不存在
     */
    ScriptConfigDTO updateScriptConfig(ScriptConfig scriptConfig);

    /**
     * 删除话术配置
     * @param id 配置ID
     * @return 是否删除成功
     * @throws RuntimeException 删除过程中发生异常或者记录不存在
     */
    boolean deleteScriptConfig(Integer id);
}