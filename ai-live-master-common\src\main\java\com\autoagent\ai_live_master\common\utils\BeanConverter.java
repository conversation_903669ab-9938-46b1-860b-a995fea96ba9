package com.autoagent.ai_live_master.common.utils;

import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * Bean转换工具类，用于处理DTO、VO、Entity等对象之间的转换
 */
public class BeanConverter {

    /**
     * 将源对象转换为目标类型对象
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标类型对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Bean转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将源对象转换为目标类型对象，支持自定义转换逻辑
     *
     * @param source 源对象
     * @param converter 自定义转换函数
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标类型对象
     */
    public static <S, T> T convert(S source, Function<S, T> converter) {
        if (source == null) {
            return null;
        }
        return converter.apply(source);
    }

    /**
     * 将源对象列表转换为目标类型对象列表
     *
     * @param sourceList 源对象列表
     * @param targetClass 目标类型
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标类型对象列表
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Class<T> targetClass) {
        if (sourceList == null) {
            return null;
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            targetList.add(convert(source, targetClass));
        }
        return targetList;
    }

    /**
     * 将源对象列表转换为目标类型对象列表，支持自定义转换逻辑
     *
     * @param sourceList 源对象列表
     * @param converter 自定义转换函数
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标类型对象列表
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Function<S, T> converter) {
        if (sourceList == null) {
            return null;
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            targetList.add(convert(source, converter));
        }
        return targetList;
    }
}