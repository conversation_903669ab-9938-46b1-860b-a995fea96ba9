package com.autoagent.ai_live_master.scriptRecommendation.service.impl;

import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptRecommendation.dto.ScriptPlanDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptPlan;
import com.autoagent.ai_live_master.scriptRecommendation.mapper.ScriptPlanMapper;
import com.autoagent.ai_live_master.scriptRecommendation.service.ScriptPlanService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 话术执行计划表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Service
@Slf4j
public class ScriptPlanServiceImpl extends ServiceImpl<ScriptPlanMapper, ScriptPlan> implements ScriptPlanService {


    @Override
    public List<ScriptPlanDTO> getAllScriptPlans() {
        Long userId = UserContext.getCurrentUserId();
        log.info("开始获取用户话术执行计划列表, 用户ID: {}", userId);
        try {
            QueryWrapper<ScriptPlan> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<ScriptPlan> scriptPlans = list(queryWrapper);
            List<ScriptPlanDTO> scriptPlanDTOs = scriptPlans.stream()
                .map(scriptPlan -> BeanConverter.convert(scriptPlan, ScriptPlanDTO.class))
                .collect(Collectors.toList());
            log.info("获取用户话术执行计划列表成功, 用户ID: {}, 计划数量: {}", userId, scriptPlanDTOs.size());
            return scriptPlanDTOs;
        } catch (Exception e) {
            log.error("获取用户话术执行计划列表失败, 用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            throw new RuntimeException("获取话术执行计划列表失败", e);
        }
    }

    @Override
    public ScriptPlanDTO createScriptPlan(ScriptPlan scriptPlan) {
        log.info("开始创建话术执行计划, 用户ID: {}", scriptPlan.getUserId());
        try {
            boolean saved = save(scriptPlan);
            if (!saved) {
                log.error("创建话术执行计划失败, 用户ID: {}", scriptPlan.getUserId());
                throw new RuntimeException("创建话术执行计划失败");
            }
            ScriptPlanDTO scriptPlanDTO = BeanConverter.convert(scriptPlan, ScriptPlanDTO.class);
            log.info("创建话术执行计划成功, ID: {}, 用户ID: {}", scriptPlan.getId(), scriptPlan.getUserId());
            return scriptPlanDTO;
        } catch (Exception e) {
            log.error("创建话术执行计划失败, 用户ID: {}, 错误信息: {}", scriptPlan.getUserId(), e.getMessage(), e);
            throw new RuntimeException("创建话术执行计划失败", e);
        }
    }

    @Override
    public ScriptPlanDTO updateScriptPlan(ScriptPlan scriptPlan) {
        log.info("开始更新话术执行计划, ID: {}, 用户ID: {}", scriptPlan.getId(), scriptPlan.getUserId());
        try {
            // 先检查记录是否存在
            ScriptPlan existingPlan = getById(scriptPlan.getId());
            if (existingPlan == null) {
                log.warn("更新话术执行计划失败, 记录不存在, ID: {}, 用户ID: {}", scriptPlan.getId(), scriptPlan.getUserId());
                throw new RuntimeException("话术执行计划不存在");
            }
            
            boolean updated = updateById(scriptPlan);
            if (!updated) {
                log.error("更新话术执行计划失败, ID: {}, 用户ID: {}", scriptPlan.getId(), scriptPlan.getUserId());
                throw new RuntimeException("更新话术执行计划失败");
            }
            
            ScriptPlan updatedPlan = getById(scriptPlan.getId());
            ScriptPlanDTO scriptPlanDTO = BeanConverter.convert(updatedPlan, ScriptPlanDTO.class);
            log.info("更新话术执行计划成功, ID: {}, 用户ID: {}", scriptPlan.getId(), scriptPlan.getUserId());
            return scriptPlanDTO;
        } catch (Exception e) {
            log.error("更新话术执行计划失败, ID: {}, 用户ID: {}, 错误信息: {}", 
                scriptPlan.getId(), scriptPlan.getUserId(), e.getMessage(), e);
            throw new RuntimeException("更新话术执行计划失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean deleteScriptPlan(Integer id) {
        log.info("开始删除话术执行计划, ID: {}", id);
        try {
            // 先检查记录是否存在
            ScriptPlan existingPlan = getById(id);
            if (existingPlan == null) {
                log.warn("删除话术执行计划失败, 记录不存在, ID: {}", id);
                throw new RuntimeException("话术执行计划不存在");
            }
            
            boolean removed = removeById(id);
            if (!removed) {
                log.error("删除话术执行计划失败, ID: {}, 用户ID: {}", id, existingPlan.getUserId());
                throw new RuntimeException("删除话术执行计划失败");
            }
            
            log.info("删除话术执行计划成功, ID: {}, 用户ID: {}", id, existingPlan.getUserId());
            return true;
        } catch (Exception e) {
            log.error("删除话术执行计划失败, ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("删除话术执行计划失败: " + e.getMessage(), e);
        }
    }
}