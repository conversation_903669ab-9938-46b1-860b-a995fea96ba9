package com.autoagent.ai_live_master.scriptGeneration.service;

import com.autoagent.ai_live_master.scriptGeneration.dto.MethodologySummaryDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologyFile;
import com.autoagent.ai_live_master.scriptGeneration.mapper.MethodologyFileMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MethodologySummaryService 测试类
 * 主要测试OSS文件上传和文件记录保存功能
 */
@SpringBootTest
@ActiveProfiles("test")
class MethodologySummaryServiceTest {

    @Test
    void testMethodologyFileEntity() {
        // 测试MethodologyFile实体类的基本功能
        MethodologyFile file = new MethodologyFile();
        file.setUserId(1L);
        file.setFileName("test.pdf");
        file.setFilePath("https://bucket.oss-region.aliyuncs.com/test.pdf");
        file.setRemarks("测试文件");
        
        assertEquals(1L, file.getUserId());
        assertEquals("test.pdf", file.getFileName());
        assertEquals("https://bucket.oss-region.aliyuncs.com/test.pdf", file.getFilePath());
        assertEquals("测试文件", file.getRemarks());
    }
    
    @Test
    void testMethodologySummaryDTO() {
        // 测试DTO的基本功能
        MethodologySummaryDTO dto = new MethodologySummaryDTO();
        dto.setUserId(1L);
        
        // 创建模拟文件
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", 
                "methodology.pdf", 
                "application/pdf", 
                "test content".getBytes()
        );
        dto.setFile(mockFile);
        
        assertEquals(1L, dto.getUserId());
        assertEquals("methodology.pdf", dto.getFile().getOriginalFilename());
        assertEquals("application/pdf", dto.getFile().getContentType());
    }
    
    @Test
    void testFilePathGeneration() {
        // 测试文件路径生成逻辑
        String originalFilename = "方法论总结.pdf";
        String expectedExtension = ".pdf";
        
        assertTrue(originalFilename.contains(expectedExtension));
        assertEquals(expectedExtension, originalFilename.substring(originalFilename.lastIndexOf(".")));
    }
}
