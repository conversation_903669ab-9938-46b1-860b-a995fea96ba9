package com.autoagent.ai_live_master.scriptRecommendation.Enum;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StyleEnum {
    NATURAL("自然流"),
    MARKET("卖场"),
    GRASS("种草");

    @JsonValue
    @EnumValue
    private final String label;

    public static StyleEnum fromLabel(String label) {
        for (StyleEnum style : values()) {
            if (style.label.equals(label)) return style;
        }
        throw new IllegalArgumentException("无效的直播风格: " + label);
    }
}
