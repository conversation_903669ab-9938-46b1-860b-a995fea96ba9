package com.autoagent.ai_live_master.scriptGeneration.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("script_feature_files")
public class ScriptFeatureFile {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String fileName;

    private String filePath;

    private String remarks;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime uploadTime;
}

