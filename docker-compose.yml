version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.node
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=production
      - NODE_OPTIONS=--max_old_space_size=4096
      - PYTHONIOENCODING=utf-8
      - LANG=zh_CN.UTF-8
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - app-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8
      - SPRING_PROFILES_ACTIVE=prod
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - CHROME_PATH=/usr/bin/google-chrome
      - CHROME_DRIVER=/usr/local/bin/chromedriver
      - VUE_APP_URL=http://frontend:5173
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - frontend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge