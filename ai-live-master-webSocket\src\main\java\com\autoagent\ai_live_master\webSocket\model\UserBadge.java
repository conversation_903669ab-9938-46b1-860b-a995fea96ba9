package com.autoagent.ai_live_master.webSocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserBadge {
    private List<String> urlList;
    private Integer imageType;
    private BadgeContent content;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BadgeContent {
        private String fontColor;
        private String level;
        private String alternativeText;
    }
}