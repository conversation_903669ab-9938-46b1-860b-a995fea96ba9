package com.autoagent.ai_live_master.webSocket.vo;

import com.autoagent.ai_live_master.webSocket.model.FansClubInfo;
import com.autoagent.ai_live_master.webSocket.model.UserBadge;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RealTimeBulletScreenMessage {
    private String type = "realTimeBulletScreen";  // 消息类型统一为realTimeBulletScreen
    private List<BulletScreenItem> value;  // 消息内容，包含用户信息、文本内容、礼物信息等
    private String id;     // 对话ID
    private Boolean complete = false;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BulletScreenItem {
        private String type;  // 消息类型：memberJoin, gift, like, chat, social, roomUserSeq
        private String userAvatar;      // 对应 LiveMessage.CastUser.avatar
        private String userName;        // 对应 LiveMessage.CastUser.name
        private Integer userLevel;      // 对应 LiveMessage.CastUser.payGrade.level
        private String comment;         // 对应 LiveMessage.content
        private String userId;          // 对应 LiveMessage.CastUser.id
        private Integer userGender;     // 对应 LiveMessage.CastUser.gender (0-未知，1-男，2-女)
        private String userShortId;     // 对应 LiveMessage.CastUser.shortId
        private String userDisplayId;   // 对应 LiveMessage.CastUser.displayId
        private String userSecUid;      // 对应 LiveMessage.CastUser.secUid
        private String userWebcastUid;  // 对应 LiveMessage.CastUser.webcastUid
        private List<UserBadge> userBadges;
        private FansClubInfo fansClubInfo;
        
        // 以下字段根据消息类型有值
        private Integer audienceCount;    // 观看人数（memberJoin, roomUserSeq类型有值）
        private String giftId;           // 礼物ID（gift类型有值）
        private String giftName;         // 礼物名称（gift类型有值）
        private String giftImage;        // 礼物图片URL（gift类型有值）
        private Integer giftCount;        // 礼物数量（gift类型有值）
        private Integer roomLikeCount;    // 本场直播点赞总数（like类型有值）
        private Integer followCount;      // 主播总关注数（social类型有值）
        private Integer totalUserCount;   // 总浏览数（roomUserSeq类型有值）
        private List<RankItem> rank;      // 榜单前三用户信息（roomUserSeq类型有值）
    }
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RankItem {
        private String nickname;  // 用户昵称
        private String avatar;    // 用户头像
        private String rank;      // 排名
    }
}