package com.autoagent.ai_live_master.scriptGeneration.vo;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PageRequestVO 测试类
 */
class PageRequestVOTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testDefaultValues() {
        PageRequestVO pageRequest = new PageRequestVO();
        
        assertEquals(1, pageRequest.getPageNum());
        assertEquals(10, pageRequest.getPageSize());
    }

    @Test
    void testValidValues() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageNum(2);
        pageRequest.setPageSize(20);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertTrue(violations.isEmpty());
    }

    @Test
    void testInvalidPageNum() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageNum(0);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertFalse(violations.isEmpty());
        assertEquals("页码必须大于0", violations.iterator().next().getMessage());
    }

    @Test
    void testInvalidPageSize() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageSize(0);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertFalse(violations.isEmpty());
        assertEquals("每页大小必须大于0", violations.iterator().next().getMessage());
    }

    @Test
    void testSettersAndGetters() {
        PageRequestVO pageRequest = new PageRequestVO();
        
        pageRequest.setPageNum(5);
        pageRequest.setPageSize(25);
        
        assertEquals(5, pageRequest.getPageNum());
        assertEquals(25, pageRequest.getPageSize());
    }
}
