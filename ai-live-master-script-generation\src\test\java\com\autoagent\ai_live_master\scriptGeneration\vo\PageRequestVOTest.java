package com.autoagent.ai_live_master.scriptGeneration.vo;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PageRequestVO 测试类
 */
class PageRequestVOTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testDefaultValues() {
        PageRequestVO pageRequest = new PageRequestVO();

        assertEquals(1, pageRequest.getPageNum());
        assertEquals(10, pageRequest.getPageSize());
        assertNull(pageRequest.getKeyword());
    }

    @Test
    void testValidValues() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageNum(2);
        pageRequest.setPageSize(20);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertTrue(violations.isEmpty());
    }

    @Test
    void testInvalidPageNum() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageNum(0);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertFalse(violations.isEmpty());
        assertEquals("页码必须大于0", violations.iterator().next().getMessage());
    }

    @Test
    void testInvalidPageSize() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setPageSize(0);
        
        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertFalse(violations.isEmpty());
        assertEquals("每页大小必须大于0", violations.iterator().next().getMessage());
    }

    @Test
    void testSettersAndGetters() {
        PageRequestVO pageRequest = new PageRequestVO();

        pageRequest.setPageNum(5);
        pageRequest.setPageSize(25);
        pageRequest.setKeyword("测试关键字");

        assertEquals(5, pageRequest.getPageNum());
        assertEquals(25, pageRequest.getPageSize());
        assertEquals("测试关键字", pageRequest.getKeyword());
    }

    @Test
    void testKeywordCanBeNull() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setKeyword(null);

        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertTrue(violations.isEmpty(), "keyword字段为null时应该通过验证");
    }

    @Test
    void testKeywordCanBeEmpty() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setKeyword("");

        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertTrue(violations.isEmpty(), "keyword字段为空字符串时应该通过验证");
    }

    @Test
    void testKeywordWithValue() {
        PageRequestVO pageRequest = new PageRequestVO();
        pageRequest.setKeyword("搜索关键字");

        Set<ConstraintViolation<PageRequestVO>> violations = validator.validate(pageRequest);
        assertTrue(violations.isEmpty(), "keyword字段有值时应该通过验证");
        assertEquals("搜索关键字", pageRequest.getKeyword());
    }

    @Test
    void testHasKeywordMethod() {
        PageRequestVO pageRequest = new PageRequestVO();

        // 测试默认情况（null）
        assertFalse(pageRequest.hasKeyword(), "keyword为null时hasKeyword应该返回false");

        // 测试空字符串
        pageRequest.setKeyword("");
        assertFalse(pageRequest.hasKeyword(), "keyword为空字符串时hasKeyword应该返回false");

        // 测试只有空格的字符串
        pageRequest.setKeyword("   ");
        assertFalse(pageRequest.hasKeyword(), "keyword为空格字符串时hasKeyword应该返回false");

        // 测试有效关键字
        pageRequest.setKeyword("测试");
        assertTrue(pageRequest.hasKeyword(), "keyword有有效值时hasKeyword应该返回true");

        // 测试有空格但有内容的关键字
        pageRequest.setKeyword("  测试关键字  ");
        assertTrue(pageRequest.hasKeyword(), "keyword有有效值（包含空格）时hasKeyword应该返回true");
    }
}
