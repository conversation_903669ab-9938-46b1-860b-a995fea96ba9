package com.autoagent.ai_live_master.scriptRecommendation.entity;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.LiveTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.PriorityEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.RecommendTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.StyleEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Handler.LiveTypeEnumTypeHandler;
import com.autoagent.ai_live_master.scriptRecommendation.Handler.PriorityEnumTypeHandler;
import com.autoagent.ai_live_master.scriptRecommendation.Handler.RecommendTypeEnumTypeHandler;
import com.autoagent.ai_live_master.scriptRecommendation.Handler.StyleEnumTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.EnumTypeHandler;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 话术生成核心配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@TableName(value = "script_config", autoResultMap = true)
public class ScriptConfig {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Long userId;

    private Integer liveRoomId;

    private String configName;

    private Integer completeScript;

    @TableField(value = "live_type", typeHandler = LiveTypeEnumTypeHandler.class)
    private LiveTypeEnum liveType;

    private String productInfo;

    @TableField(typeHandler = StyleEnumTypeHandler.class)
    private StyleEnum style;

    private Boolean exaggeration;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private DataAlertConfig dataAlerts;

    @TableField(typeHandler = PriorityEnumTypeHandler.class)
    private PriorityEnum priority;

    @TableField(typeHandler = RecommendTypeEnumTypeHandler.class)
    private RecommendTypeEnum recommendType;

    private Integer scriptConfigForbidden;

    private Integer scriptConfigBenefit;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private ShieldConfig shieldConfig;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private TargetAudience targetAudience;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createdAt ;

    // ✅ 嵌套类：数据提示
    @Data
    public static class DataAlertConfig {
        private List<Condition> conditions;

        @Data
        public static class Condition {
            private String metric;
            private String operator;
            private Integer value;
        }
    }

    // ✅ 嵌套类：屏蔽配置
    @Data
    public static class ShieldConfig {
        private List<String> keywords;
        @JsonProperty("shield_minutes")  // 显式指定 JSON 中字段名
        private Integer shieldMinutes;
    }

    // ✅ 嵌套类：人群定向
    @Data
    public static class TargetAudience {
        private String age;
        private String gender;
    }
}
