# AI直播助手 API文档

## 目录
1. [用户管理](#1-用户管理)
2. [直播间管理](#2-直播间管理)
3. [违禁词管理](#3-违禁词管理)
4. [电商权益管理](#4-电商权益管理)
5. [话术配置管理](#5-话术配置管理)
6. [话术计划管理](#6-话术计划管理)
7. [话术展示](#7-话术展示)
8. [系统健康检查](#8-系统健康检查)
9. [认证管理](#9-认证管理)
10. [方法论总结](#10-方法论总结)
11. [话术片段](#11-话术片段)
12. [话术大纲](#12-话术大纲)
13. [完整话术](#13-完整话术)
14. [话术特征](#14-话术特征)
15. [产品素材](#15-产品素材)

## 1. 用户管理

### 1.1 获取所有用户
- **接口地址**：`/api/v1/users`
- **请求方式**：GET
- **接口说明**：获取系统中所有用户列表，不包含用户密码等敏感信息。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "id": 1,
              "userid": "john_doe"
          },
          {
              "id": 2,
              "userid": "jane_smith"
          }
      ],
      "timestamp": "2025-05-20T13:55:01"
  }
  ```

### 1.2 根据用户ID获取用户
- **接口地址**：`/api/v1/users/{id}`
- **请求方式**：GET
- **接口说明**：根据用户数据库主键 ID 获取单个用户信息。
- **路径参数**：
  - id（int）：用户主键 ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "id": 1,
          "userid": "john_doe"
      },
      "timestamp": "2025-05-20T13:58:51"
  }
  ```

### 1.3 根据用户名获取用户
- **接口地址**：`/api/v1/users/userid/{userid}`
- **请求方式**：GET
- **接口说明**：根据用户名（userid）查询用户信息，常用于用户唯一性校验等。
- **路径参数**：
  - userid（string）：用户登录账号/用户名
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "id": 4,
          "userid": "root"
      },
      "timestamp": "2025-05-20T14:00:29"
  }
  ```

### 1.4 创建用户
- **接口地址**：`/api/v1/users`
- **请求方式**：POST
- **接口说明**：用于创建新用户，若用户名（userid）重复会返回冲突错误。
- **请求体参数**：
  ```json
  {
    "userid": "newuser",
    "userPassword": "123456"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "用户创建成功",
      "data": {
          "id": 8,
          "userid": "autoagentroot"
      },
      "timestamp": "2025-05-20T14:04:01"
  }
  ```

### 1.5 更新用户
- **接口地址**：`/api/v1/users/{id}`
- **请求方式**：PUT
- **接口说明**：根据 ID 更新用户信息（如用户名、密码等）。
- **路径参数**：
  - id（int）：用户主键 ID
- **请求体参数**：
  ```json
  {
    "userid": "updateduser",
    "userPassword": "newpassword"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "用户更新成功",
      "data": {
          "id": 8,
          "userid": "autoagentroot"
      },
      "timestamp": "2025-05-20T14:06:35"
  }
  ```

### 1.6 删除用户
- **接口地址**：`/api/v1/users/{id}`
- **请求方式**：DELETE
- **接口说明**：根据用户主键 ID 删除指定用户。
- **路径参数**：
  - id（int）：用户主键 ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "用户删除成功",
      "data": null,
      "timestamp": "2025-05-20T14:09:41"
  }
  ```

## 2. 直播间管理

### 2.1 创建直播间
- **接口地址**：`/api/v1/live_rooms`
- **请求方式**：POST
- **接口说明**：创建一个新的直播间，包含账号信息、密钥信息和所属平台。
- **请求体参数**：
  ```json
  {
    "accountInfo": {
      "account_name": "抖音号001",
      "uid": "dy12345"
    },
    "secretKey": {
      "app_id": "app123",
      "app_secret": "secretABC"
    },
    "platform": "抖音"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": null,
      "timestamp": "2025-05-20T16:56:22"
  }
  ```

### 2.2 获取直播间列表
- **接口地址**：`/api/v1/live_rooms`
- **请求方式**：GET
- **接口说明**：获取当前用户的所有直播间信息，返回不包含密钥的脱敏结果。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "userId": 4,
              "accountInfo": {
                  "uid": "dy12345",
                  "account_name": "抖音号001"
              },
              "platform": "抖音",
              "createdAt": "2025-05-20T15:19:13",
              "updatedAt": "2025-05-20T15:19:13"
          }
      ],
      "timestamp": "2025-05-20T16:57:40"
  }
  ```

### 2.3 更新直播间信息
- **接口地址**：`/api/v1/live_rooms/{id}`
- **请求方式**：PUT
- **接口说明**：根据 ID 更新指定直播间的信息。
- **路径参数**：
  - id（int）：直播间ID
- **请求体参数**：
  ```json
  {
    "accountInfo": {
      "account_name": "抖音号001",
      "uid": "dy12345"
    },
    "secretKey": {
      "app_id": "app123",
      "app_secret": "secretABC"
    },
    "platform": "抖音"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "userId": 4,
          "accountInfo": {
              "xx": "4"
          },
          "platform": "阿里云",
          "createdAt": "2025-05-20T16:30:06",
          "updatedAt": "2025-05-20T17:01:56"
      },
      "timestamp": "2025-05-20T17:01:56"
  }
  ```

### 2.4 删除直播间
- **接口地址**：`/api/v1/live_rooms/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定ID的直播间。
- **路径参数**：
  - id（int）：直播间ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": "删除成功",
      "timestamp": "2025-05-20T17:03:55"
  }
  ```

## 3. 违禁词管理

### 3.1 创建违禁词库
- **接口地址**：`/api/v1/forbidden_terms`
- **请求方式**：POST
- **接口说明**：为当前用户创建一个新的违禁词词库。
- **请求体参数**：
  ```json
  {
    "term_name": "string, 必填, 词库名称",
    "terms": ["string, 必填, 违禁词列表，支持多个"]
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": null,
      "timestamp": "2025-05-21T17:21:40"
  }
  ```

### 3.2 获取违禁词库列表
- **接口地址**：`/api/v1/forbidden_terms`
- **请求方式**：GET
- **接口说明**：获取当前用户创建的所有违禁词词库列表。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "id": 1,
              "userId": 4,
              "termName": "test违禁词库",
              "terms": [
                  "sm",
                  "s",
                  "m"
              ],
              "createdAt": "2025-05-20T17:57:57"
          }
      ],
      "timestamp": "2025-05-21T17:32:33"
  }
  ```

### 3.3 更新违禁词库
- **接口地址**：`/api/v1/forbidden_terms/{id}`
- **请求方式**：PUT
- **接口说明**：根据 ID 更新指定违禁词词库的名称或词条。
- **路径参数**：
  - id（int）：违禁词库ID
- **请求体参数**：
  ```json
  { 
    "termName": "testone",
    "terms": [
      "sm","s","m","sss"
    ]
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "id": 5,
          "userId": 4,
          "termName": "testone",
          "terms": [
              "sm",
              "s",
              "m",
              "sss"
          ],
          "createdAt": "2025-05-21T17:21:28"
      },
      "timestamp": "2025-05-21T17:33:17"
  }
  ```

### 3.4 删除违禁词库
- **接口地址**：`/api/v1/forbidden_terms/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定 ID 的违禁词词库。
- **路径参数**：
  - id（int）：违禁词库ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": "删除成功",
      "timestamp": "2025-05-21T17:37:35"
  }
  ```

## 4. 电商权益管理

### 4.1 创建电商权益
- **接口地址**：`/api/v1/ecommerce_benefits`
- **请求方式**：POST
- **接口说明**：创建一个新的电商权益，绑定到当前用户下。
- **请求体参数**：
  ```json
  {
    "benefitName": "618大促",
    "benefits": {
      "type":"折扣",
      "value":85
    }
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "id": 5,
          "userId": 4,
          "benefitName": "618大促",
          "benefits": {
              "type": "折扣",
              "value": 85
          },
          "createdAt": null
      },
      "timestamp": "2025-05-23T10:32:50"
  }
  ```

### 4.2 获取电商权益列表
- **接口地址**：`/api/v1/ecommerce_benefits`
- **请求方式**：GET
- **接口说明**：获取当前用户的全部电商权益信息列表。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "id": 1,
              "userId": 4,
              "benefitName": "基础权益666",
              "benefits": {
                  "type": "折扣haha",
                  "value": 856
              },
              "createdAt": "2025-05-21T10:21:23.000+00:00"
          }
      ],
      "timestamp": "2025-05-23T10:36:57"
  }
  ```

### 4.3 更新电商权益
- **接口地址**：`/api/v1/ecommerce_benefits/{id}`
- **请求方式**：PUT
- **接口说明**：根据 ID 更新电商权益内容，仅限当前用户的权益记录。
- **路径参数**：
  - id（int）：电商权益ID
- **请求体参数**：
  ```json
  {
    "benefitName": "618大促1",
    "benefits": {
      "type":"折扣",
      "value":85
    }
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": {
          "id": 5,
          "userId": 4,
          "benefitName": "618超级大促",
          "benefits": {
              "type": "折扣",
              "value": 75
          },
          "createdAt": null
      },
      "timestamp": "2025-05-23T10:40:38"
  }
  ```

### 4.4 删除电商权益
- **接口地址**：`/api/v1/ecommerce_benefits/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定 ID 的电商权益，仅支持当前用户下的记录。
- **路径参数**：
  - id（int）：电商权益ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": "删除成功",
      "timestamp": "2025-05-23T10:42:43"
  }
  ```

## 5. 话术配置管理

### 5.1 创建话术配置
- **接口地址**：`/api/v1/script_config`
- **请求方式**：POST
- **接口说明**：创建电商话术推荐配置（ScriptConfig），用于直播中按特定策略推荐商品话术。
- **请求体参数**：
  ```json
  {
    "liveRoomId": 4,
    "configName": "苹果手机专属",
    "completeScript": 1,
    "liveType": "单品",
    "productInfo": "智能手机机制",
    "style": "自然流",
    "exaggeration": true,
    "dataAlerts": {
      "conditions": [
        {
          "metric": "进线人数",
          "operator": ">",
          "value": 100
        }
      ]
    },
    "priority": "异动数据优先",
    "recommendType": "系统推荐",
    "scriptConfigForbidden": 4,
    "scriptConfigBenefit": 5,
    "shieldConfig": {
      "keywords": [
        "抽奖",
        "免单"
      ],
      "shield_minutes": 5
    },
    "targetAudience": {
      "age": "18-23",
      "gender": "女"
    }
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "创建话术配置成功",
      "data": {
          "id": 18,
          "userId": 4,
          "liveRoomId": 4,
          "configName": "苹果手机专属",
          "completeScript": 1,
          "liveType": "单品",
          "productInfo": "智能手机机制",
          "style": "自然流",
          "exaggeration": true,
          "dataAlerts": {
              "conditions": [
                  {
                      "metric": "进线人数",
                      "operator": ">",
                      "value": 100
                  }
              ]
          },
          "priority": "异动数据优先",
          "recommendType": "系统推荐",
          "scriptConfigForbidden": 4,
          "scriptConfigBenefit": 5,
          "shieldConfig": {
              "keywords": [
                  "抽奖",
                  "免单"
              ],
              "shield_minutes": 5
          },
          "targetAudience": {
              "age": "18-23",
              "gender": "女"
          },
          "createdAt": "2025-05-23T16:26:28.8629607"
      },
      "timestamp": "2025-05-23T16:26:29"
  }
  ```

### 5.2 获取话术配置列表
- **接口地址**：`/api/v1/script-config`
- **请求方式**：GET
- **接口说明**：获取当前登录用户的所有话术推荐配置。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "id": 8,
              "userId": 4,
              "liveRoomId": 4,
              "configName": "爆款手机推荐配置",
              "completeScript": 1,
              "liveType": "单品",
              "productInfo": "智能手机机制",
              "style": "自然流",
              "exaggeration": true,
              "dataAlerts": {
                  "conditions": [
                      {
                          "metric": "进线人数",
                          "operator": ">",
                          "value": 100
                      }
                  ]
              },
              "priority": "异动数据优先",
              "recommendType": "系统推荐",
              "scriptConfigForbidden": 4,
              "scriptConfigBenefit": 5,
              "shieldConfig": {
                  "keywords": [
                      "抽奖"
                  ],
                  "shield_minutes": 5
              },
              "targetAudience": {
                  "age": "18-23",
                  "gender": "女"
              },
              "createdAt": "2025-05-23T14:55:24"
          }
      ],
      "timestamp": "2025-05-23T16:32:17"
  }
  ```

### 5.3 更新话术配置
- **接口地址**：`/api/v1/script-config/{id}`
- **请求方式**：PUT
- **接口说明**：更新指定 ID 的话术推荐配置。
- **路径参数**：
  - id（int）：话术配置ID
- **请求体参数**：
  ```json
  {
    "liveRoomId": 4,
    "configName": "苹果手机专属1",
    "completeScript": 1,
    "liveType": "单品",
    "productInfo": "智能手机机制",
    "style": "自然流",
    "exaggeration": true,
    "dataAlerts": {
      "conditions": [
        {
          "metric": "进线人数",
          "operator": ">",
          "value": 100
        }
      ]
    },
    "priority": "异动数据优先",
    "recommendType": "系统推荐",
    "scriptConfigForbidden": 4,
    "scriptConfigBenefit": 5,
    "shieldConfig": {
      "keywords": [
        "抽奖",
        "免单"
      ],
      "shield_minutes": 5
    },
    "targetAudience": {
      "age": "18-23",
      "gender": "男"
    }
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "更新话术配置成功",
      "data": {
          "id": 18,
          "userId": 4,
          "liveRoomId": 4,
          "configName": "苹果手机专属1",
          "completeScript": 1,
          "liveType": "单品",
          "productInfo": "智能手机机制",
          "style": "自然流",
          "exaggeration": true,
          "dataAlerts": {
              "conditions": [
                  {
                      "metric": "进线人数",
                      "operator": ">",
                      "value": 100
                  }
              ]
          },
          "priority": "异动数据优先",
          "recommendType": "系统推荐",
          "scriptConfigForbidden": 4,
          "scriptConfigBenefit": 5,
          "shieldConfig": {
              "keywords": [
                  "抽奖",
                  "免单"
              ],
              "shield_minutes": 5
          },
          "targetAudience": {
              "age": "18-23",
              "gender": "男"
          },
          "createdAt": "2025-05-23T16:36:06"
      },
      "timestamp": "2025-05-23T16:36:06"
  }
  ```

### 5.4 删除话术配置
- **接口地址**：`/api/v1/script-config/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定的话术推荐配置。
- **路径参数**：
  - id（int）：话术配置ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "删除话术配置成功",
      "data": null,
      "timestamp": "2025-05-23T16:38:50"
  }
  ```

## 6. 话术计划管理

### 6.1 创建话术执行计划
- **接口地址**：`/api/v1/script-plan`
- **请求方式**：POST
- **接口说明**：创建一个新的话术执行计划。
- **请求体参数**：
  ```json
  {
    "planName": "523 爆款直播计划",
    "scriptConfigId": 8,
    "isActive": true,
    "startTime": "2025-06-01T09:00:00",
    "endTime": "2025-06-18T23:00:00",
    "createdAt": "2025-05-23T17:14:02"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "创建话术执行计划成功",
      "data": {
          "id": 6,
          "userId": 4,
          "planName": "523 爆款直播计划",
          "scriptConfigId": 8,
          "isActive": true,
          "startTime": "2025-06-01T09:00:00",
          "endTime": "2025-06-18T23:00:00",
          "createdAt": "2025-05-23T17:34:05.526171"
      },
      "timestamp": "2025-05-23T17:34:05"
  }
  ```

### 6.2 获取话术执行计划列表
- **接口地址**：`/api/v1/script-plan`
- **请求方式**：GET
- **接口说明**：获取当前登录用户创建的所有话术执行计划。
- **请求参数**：无
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "success",
      "data": [
          {
              "id": 1,
              "userId": 4,
              "planName": "618 爆款直播计划",
              "scriptConfigId": 8,
              "isActive": true,
              "startTime": "2025-06-01T09:00:00",
              "endTime": "2025-06-18T23:00:00",
              "createdAt": "2025-05-23T17:14:02"
          }
      ],
      "timestamp": "2025-05-23T17:36:32"
  }
  ```

### 6.3 更新话术执行计划
- **接口地址**：`/api/v1/script-plan/{id}`
- **请求方式**：PUT
- **接口说明**：更新指定 ID 的话术执行计划。
- **路径参数**：
  - id（int）：话术执行计划ID
- **请求体参数**：
  ```json
  {
    "planName": "爆款直播计划",
    "scriptConfigId": 8,
    "isActive": true,
    "startTime": "2025-06-01T09:00:00",
    "endTime": "2025-06-18T23:00:00",
    "createdAt": "2025-05-23T17:14:02"
  }
  ```
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "更新话术执行计划成功",
      "data": {
          "id": 6,
          "userId": 4,
          "planName": "爆款直播计划",
          "scriptConfigId": 8,
          "isActive": true,
          "startTime": "2025-06-01T09:00:00",
          "endTime": "2025-06-18T23:00:00",
          "createdAt": "2025-05-23T17:39:10"
      },
      "timestamp": "2025-05-23T17:39:09"
  }
  ```

### 6.4 删除话术执行计划
- **接口地址**：`/api/v1/script-plan/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定 ID 的话术执行计划。
- **路径参数**：
  - id（int）：话术执行计划ID
- **响应示例**：
  ```json
  {
      "code": 200,
      "message": "删除话术执行计划成功",
      "data": null,
      "timestamp": "2025-05-23T17:40:44"
  }
  ```

## 7. 话术展示

### 7.1 获取推荐话术
- **接口地址**：`/api/recommend_scripts`
- **请求方式**：POST
- **接口说明**：获取推荐话术列表。
- **请求体参数**：
  ```json
  {
    "live_room_id": "number, 必填, 直播间ID",
    "script_config_id": "number, 必填, 话术配置ID",
    "current_scripts": "object, 可选, 最近使用的话术、定时话术位置、定时话术内容"
  }
  ```
- **响应示例**：
  ```json
  {
    "scripts": [
      {
        "id": "rec001",
        "content": "新进来的宝宝们注意啦！现在直播间有85折优惠，点击下方购物车就能看到！",
        "priority": 1
      },
      {
        "id": "rec002",
        "content": "很多宝宝在问色号，这款是#405烂番茄色，黄皮显白神器！",
        "priority": 2
      }
    ]
  }
  ```

### 7.2 获取直播间实时数据
- **接口地址**：`/api/live_rooms/:id/realtime_data`
- **请求方式**：GET
- **接口说明**：获取直播间实时数据。
- **路径参数**：
  - id（int）：直播间ID
- **响应示例**：
  ```json
  {
    "live_room_id": 1,
    "timestamp": "2023-06-18T14:35:00Z",
    "basic_metrics": {
      "online_users": 152,
      "new_users": 32
    },
    "commerce_metrics": {
      "order_count": 15
    },
    "audience_metrics": null,
    "product_metrics": null
  }
  ```

## 8. 系统健康检查

### 8.1 健康检查
- **接口地址**：`/health`
- **请求方式**：GET
- **接口说明**：检查系统健康状态。
- **请求参数**：无
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "application": "ai_live_master",
      "version": "1.0.0",
      "status": "UP",
      "timestamp": 1747192780228
    },
    "timestamp": "2025-05-14T11:19:40"
  }
  ```

## 9. 认证管理

### 9.1 用户登录
- **接口地址**：`/auth/login`
- **请求方式**：POST
- **接口说明**：用户登录接口，返回JWT令牌
- **请求体参数**：
  ```json
  {
    "username": "用户名",
    "password": "密码"
  }
  ```
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "eyJhbGciOiJIUzI1NiJ9...",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 9.2 获取Token
- **接口地址**：`/api/token/get`
- **请求方式**：POST
- **接口说明**：根据AccessKey ID和Secret获取Token
- **请求体参数**：
  ```json
  {
    "accessKeyId": "AccessKey ID",
    "accessKeySecret": "AccessKey Secret"
  }
  ```
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "token": "获取到的token",
      "expireTime": 1747192780228,
      "expireDate": "2025-05-14 11:19:40"
    },
    "timestamp": "2024-03-21T10:00:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 400,
    "message": "获取Token失败",
    "data": null,
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 10. 方法论总结

### 10.1 上传方法论总结
- **接口地址**：`/methodology-summaries/upload`
- **请求方式**：POST
- **接口说明**：上传方法论总结文件
- **请求参数**：
  - 文件：multipart/form-data格式
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "文件上传成功",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 10.2 获取方法论总结列表
- **接口地址**：`/methodology-summaries/list`
- **请求方式**：GET
- **接口说明**：获取当前登录用户的所有方法论总结记录
- **请求参数**：无
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "userId": 4,
        "techniqueSummary": "方法论总结内容..."
      },
      {
        "id": 2,
        "userId": 4,
        "techniqueSummary": "方法论总结内容..."
      }
    ],
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 10.3 获取单个方法论总结
- **接口地址**：`/methodology-summaries/{id}`
- **请求方式**：GET
- **接口说明**：根据方法论总结ID获取详细信息
- **路径参数**：
  - id（number）：方法论总结ID
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "userId": 4,
      "techniqueSummary": "方法论总结内容..."
    },
    "timestamp": "2024-03-21T10:00:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 404,
    "message": "未找到指定的方法论总结",
    "data": null,
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 11. 话术片段

### 11.1 生成话术片段
- **接口地址**：`/script-fragments`
- **请求方式**：POST
- **接口说明**：根据选择的内容生成片段话术
- **请求体参数**：
  ```json
  {
    "content": "选择的内容"
  }
  ```
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "生成的话术片段内容",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 12. 话术大纲

### 12.1 生成话术大纲
- **接口地址**：`/script-outlines`
- **请求方式**：POST
- **接口说明**：生成话术大纲
- **请求体参数**：
  ```json
  {
    "content": "大纲内容"
  }
  ```
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "生成的话术大纲",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 13. 完整话术

### 13.1 保存完整话术
- **接口地址**：`/complete-scripts/save`
- **请求方式**：POST
- **接口说明**：保存完整的话术内容
- **请求参数**：
  - 文件：multipart/form-data格式
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "话术内容保存成功",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 13.2 根据ID获取话术
- **接口地址**：`/complete-scripts/{id}`
- **请求方式**：GET
- **接口说明**：根据话术ID获取完整的话术内容
- **路径参数**：
  - id（number）：话术ID
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "userId": 4,
      "title": "618大促话术",
      "content": "话术内容...",
      "version": 1,
      "createdAt": "2024-03-21T10:00:00",
      "updatedAt": "2024-03-21T10:00:00"
    },
    "timestamp": "2024-03-21T10:00:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 404,
    "message": "未找到指定的话术",
    "data": null,
    "timestamp": "2024-03-21T10:00:00"
  }
  ```
  ```json
  {
    "code": 403,
    "message": "无权访问该话术",
    "data": null,
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 13.3 修改话术
- **接口地址**：`/complete-scripts/{id}`
- **请求方式**：PUT
- **接口说明**：修改指定ID的话术内容
- **路径参数**：
  - id（number）：话术ID
- **请求体参数**：
  ```json
  {
    "title": "修改后的话术标题",
    "content": "修改后的话术内容"
  }
  ```
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "userId": 4,
      "title": "修改后的话术标题",
      "content": "修改后的话术内容",
      "version": 2,
      "createdAt": "2024-03-21T10:00:00",
      "updatedAt": "2024-03-21T10:30:00"
    },
    "timestamp": "2024-03-21T10:30:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 404,
    "message": "未找到指定的话术",
    "data": null,
    "timestamp": "2024-03-21T10:30:00"
  }
  ```
  ```json
  {
    "code": 403,
    "message": "无权修改该话术",
    "data": null,
    "timestamp": "2024-03-21T10:30:00"
  }
  ```

### 13.4 删除话术
- **接口地址**：`/complete-scripts/{id}`
- **请求方式**：DELETE
- **接口说明**：删除指定ID的话术
- **路径参数**：
  - id（number）：话术ID
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "话术删除成功",
    "timestamp": "2024-03-21T10:35:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 404,
    "message": "未找到指定的话术",
    "data": null,
    "timestamp": "2024-03-21T10:35:00"
  }
  ```
  ```json
  {
    "code": 403,
    "message": "无权删除该话术",
    "data": null,
    "timestamp": "2024-03-21T10:35:00"
  }
  ```

## 14. 话术特征

### 14.1 上传话术特征
- **接口地址**：`/script-features/upload`
- **请求方式**：POST
- **接口说明**：上传话术特征文件
- **请求参数**：
  - 文件：multipart/form-data格式
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "文件上传成功",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 14.2 获取话术特征列表
- **接口地址**：`/script-features/list`
- **请求方式**：GET
- **接口说明**：获取当前登录用户的所有话术特征记录
- **请求参数**：无
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "userId": 4,
        "scriptFeatures": "话术特征内容..."
      },
      {
        "id": 2,
        "userId": 4,
        "scriptFeatures": "话术特征内容..."
      }
    ],
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

### 14.3 获取单个话术特征
- **接口地址**：`/script-features/{id}`
- **请求方式**：GET
- **接口说明**：根据话术特征ID获取详细信息
- **路径参数**：
  - id（number）：话术特征ID
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "userId": 4,
      "scriptFeatures": "话术特征内容..."
    },
    "timestamp": "2024-03-21T10:00:00"
  }
  ```
- **错误响应**：
  ```json
  {
    "code": 404,
    "message": "未找到指定的话术特征",
    "data": null,
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 15. 产品素材

### 15.1 上传产品素材
- **接口地址**：`/product-materials/upload`
- **请求方式**：POST
- **接口说明**：上传产品相关素材文件
- **请求参数**：
  - 文件：multipart/form-data格式
  - kbId：知识库ID
- **响应示例**：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "文件上传成功",
    "timestamp": "2024-03-21T10:00:00"
  }
  ```

## 错误码说明
- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

## 注意事项
1. 所有API都需要进行Bearer Token认证
2. 请求参数中的必填项必须提供，否则会返回400错误
3. 文件上传接口支持的最大文件大小限制为10MB
4. 所有时间戳格式为ISO 8601标准格式
5. 所有ID字段均为64位整数 