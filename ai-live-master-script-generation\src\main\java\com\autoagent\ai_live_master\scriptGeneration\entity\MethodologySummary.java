package com.autoagent.ai_live_master.scriptGeneration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("methodology_summaries")
public class MethodologySummary {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private String techniqueSummary;
}