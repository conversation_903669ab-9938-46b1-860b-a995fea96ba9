package com.autoagent.ai_live_master.common.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

@Getter
@RequiredArgsConstructor
public class ByteArrayMultipartFile implements MultipartFile {

    @NonNull
    private final byte[] content;

    @NonNull
    private final String name;

    @NonNull
    private final String originalFilename;

    @NonNull
    private final String contentType;

    @Override
    public boolean isEmpty() {
        return content.length == 0;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @Override
    @NonNull
    public byte[] getBytes() {
        return content;
    }

    @Override
    @NonNull
    public InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    @Override
    public void transferTo(File dest) throws IOException {
        try (OutputStream os = new FileOutputStream(dest)) {
            os.write(content);
        }
    }
}
