package com.autoagent.ai_live_master.webSocket.vo;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Token请求参数")
public class TokenRequest {
    
    @Schema(description = "AccessKey ID", required = true)
    private String accessKeyId;
    
    @Schema(description = "AccessKey Secret", required = true)
    private String accessKeySecret;

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
} 