package com.autoagent.ai_live_master.scriptRecommendation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 * 电商权益配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
//@EqualsAndHashCode(callSuper = false)
@TableName(value = "ecommerce_benefits", autoResultMap = true)
public class EcommerceBenefits implements Serializable {
//
//    @Serial
//    private static final long serialVersionUID = 1L;

    /**
     * 权益配置唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 权益名称
     */
    private String benefitName;

    /**
     * 权益配置，格式：{"type":"折扣","value":85}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String,Object> benefits; // Storing JSON as String, consider using a JSON type or a custom type handler if your DB supports it and MyBatis is configured for it.

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createdAt;


}