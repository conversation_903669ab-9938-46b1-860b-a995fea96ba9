package com.autoagent.ai_live_master.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "agent")
public class AgentConfig {

    private ScriptAgent scriptAgent;
    private MethodologyAgent methodologyAgent;
    private OutlineAgent outlineAgent;
    private FragmentAgent fragmentAgent;
    private CompleteAgent completeAgent;
    private ProductSearchAgent productSearchAgent;
    private CompleteScriptsExtractAgent completeScriptsExtractAgent;

    @Data
    public static class ScriptAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class MethodologyAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class OutlineAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class FragmentAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class CompleteAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class ProductSearchAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }

    @Data
    public static class CompleteScriptsExtractAgent {
        private String uuid;
        private String authKey;
        private String authSecret;
    }
}