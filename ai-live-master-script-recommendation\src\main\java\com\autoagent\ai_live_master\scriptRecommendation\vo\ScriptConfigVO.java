package com.autoagent.ai_live_master.scriptRecommendation.vo;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.LiveTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.PriorityEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.RecommendTypeEnum;
import com.autoagent.ai_live_master.scriptRecommendation.Enum.StyleEnum;
import com.autoagent.ai_live_master.scriptRecommendation.entity.ScriptConfig;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <p>
 * 话术生成核心配置表 VO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class ScriptConfigVO {

    @NotNull(message = "直播间ID不能为空")
    private Integer liveRoomId;

    @NotBlank(message = "配置名称不能为空")
    private String configName;

    private Integer completeScript;

    @NotNull(message = "直播类型不能为空")
    private LiveTypeEnum liveType;

    private String productInfo;

    private StyleEnum style;

    private Boolean exaggeration;

    private ScriptConfig.DataAlertConfig dataAlerts;

    private PriorityEnum priority;

    private RecommendTypeEnum recommendType;

    private Integer scriptConfigForbidden;

    private Integer scriptConfigBenefit;

    private ScriptConfig.ShieldConfig shieldConfig;

    private ScriptConfig.TargetAudience targetAudience;

}