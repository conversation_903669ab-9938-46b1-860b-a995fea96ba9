package com.autoagent.ai_live_master.scriptRecommendation.Handler;

import com.autoagent.ai_live_master.scriptRecommendation.Enum.PriorityEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;

public class PriorityEnumTypeHandler extends BaseTypeHandler<PriorityEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, PriorityEnum parameter, JdbcType jdbcType) throws SQLException {
        // 写入数据库时，存储label
        ps.setString(i, parameter.getLabel());
    }

    @Override
    public PriorityEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String label = rs.getString(columnName);
        if (label == null) return null;
        return PriorityEnum.fromLabel(label);
    }

    @Override
    public PriorityEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String label = rs.getString(columnIndex);
        if (label == null) return null;
        return PriorityEnum.fromLabel(label);
    }

    @Override
    public PriorityEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String label = cs.getString(columnIndex);
        if (label == null) return null;
        return PriorityEnum.fromLabel(label);
    }
}
