package com.autoagent.ai_live_master.webSocket.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendedScriptMessage {

    private final String type = "recommendedScript"; // 固定类型
    private String content;   // 推荐话术内容
    private String keywords;  // 涉及关键词
    private boolean complete; // 是否完整生成
    private String id;        // 唯一标识
}

