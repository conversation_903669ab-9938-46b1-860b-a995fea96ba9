package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptGeneration.dto.UserDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.User;
import com.autoagent.ai_live_master.scriptGeneration.service.UserService;
import com.autoagent.ai_live_master.scriptGeneration.vo.PageRequestVO;
import com.autoagent.ai_live_master.scriptGeneration.vo.UserVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@Slf4j
@Tag(name = "03-用户管理", description = "用户的创建、查询和管理相关接口")
@RestController
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Operation(summary = "创建用户", description = "创建新的用户账号")
    @PostMapping
    public ApiResponse<UserDTO> create(
            @Parameter(description = "用户信息", required = true)
            @Valid @RequestBody UserVO vo) {
        try {
            User entity = BeanConverter.convert(vo, User.class);
            return ApiResponse.success(userService.createUser(entity));
        } catch (Exception e) {
            log.error("创建用户失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "创建用户失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户列表", description = "分页获取所有用户信息，支持关键字搜索")
    @GetMapping
    public ApiResponse<Page<UserDTO>> list(@ModelAttribute PageRequestVO pageRequest) {
        try {
            log.info("获取用户列表 - 页码: {}, 每页大小: {}, 关键字: {}",
                    pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());
            Page<UserDTO> users = userService.getPageUsers(
                    pageRequest.getPageNum(),
                    pageRequest.getPageSize(),
                    pageRequest.getKeyword());
            return ApiResponse.success(users);
        } catch (Exception e) {
            log.error("获取用户列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取用户列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新用户信息", description = "更新指定ID的用户信息")
    @PutMapping("/{id}")
    public ApiResponse<UserDTO> update(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Integer id,
            @Parameter(description = "用户信息", required = true)
            @Valid @RequestBody UserVO vo) {
        try {
            User entity = BeanConverter.convert(vo, User.class);
            entity.setId(id);
            return ApiResponse.success(userService.updateUser(entity));
        } catch (Exception e) {
            log.error("更新用户失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "更新用户失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除用户", description = "删除指定ID的用户")
    @DeleteMapping("/{id}")
    public ApiResponse<Boolean> delete(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Integer id) {
        try {
            return ApiResponse.success(userService.deleteUser(id));
        } catch (Exception e) {
            log.error("删除用户失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "删除用户失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户详情", description = "根据ID获取用户详细信息")
    @GetMapping("/{id}")
    public ApiResponse<UserDTO> getById(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Integer id) {
        try {
            return ApiResponse.success(userService.getUserById(id));
        } catch (Exception e) {
            log.error("获取用户详情失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取用户详情失败: " + e.getMessage());
        }
    }
}