package com.autoagent.ai_live_master.webSocket.model;

import lombok.Getter;

/**
 * 单词统计结构体：记录词频 + 最后更新时间
 */
@Getter
public class WordStats {
    private int count;
    private long lastUpdateTime;

    public WordStats(int count, long lastUpdateTime) {
        this.count = count;
        this.lastUpdateTime = lastUpdateTime;
    }

    public void increment() {
        this.count++;
        this.lastUpdateTime = System.currentTimeMillis();
    }
}
