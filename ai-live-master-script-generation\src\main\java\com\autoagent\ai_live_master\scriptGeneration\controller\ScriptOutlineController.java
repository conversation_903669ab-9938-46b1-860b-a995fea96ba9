package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptGeneration.dto.ScriptOutlineDTO;
import com.autoagent.ai_live_master.scriptGeneration.service.ScriptOutlineService;
import com.autoagent.ai_live_master.scriptGeneration.vo.ScriptOutlineRequestVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "15-话术大纲管理", description = "话术大纲的生成和管理相关接口")
@RestController
@RequestMapping("/script-outlines")
public class ScriptOutlineController {

    @Autowired
    private ScriptOutlineService scriptOutlineService;

    @Operation(summary = "生成话术大纲", description = "根据输入内容生成话术大纲")
    @PostMapping
    public ApiResponse<String> generateOutline(
            @Parameter(description = "大纲生成参数", required = true)
            @RequestBody ScriptOutlineRequestVO requestVO) {
        try {
            ScriptOutlineDTO dto = BeanConverter.convert(requestVO, ScriptOutlineDTO.class);
            dto.setUserId(UserContext.getCurrentUserId());
            String outline = scriptOutlineService.generateOutline(dto);
            return ApiResponse.success(outline);
        } catch (Exception e) {
            log.error("生成话术大纲失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "生成话术大纲失败: " + e.getMessage());
        }
    }
}