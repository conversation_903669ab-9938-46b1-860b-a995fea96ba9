#!/bin/bash

echo "==================================="
echo "正在停止服务..."
echo "==================================="

# 停止Node.js服务
echo "停止Node.js服务..."
NODE_PID=$(ps aux | grep -v grep | grep "npm run dev" | awk '{print $2}')
if [ -n "$NODE_PID" ]; then
    echo "找到Node.js进程PID: $NODE_PID，正在停止..."
    kill -9 $NODE_PID
    echo "Node.js服务已停止"
else
    echo "未找到运行的Node.js服务"
fi

# 停止Java服务
echo "停止Java服务..."
JAVA_PID=$(ps aux | grep -v grep | grep "ai-live-master-app-1.0-SNAPSHOT.jar" | awk '{print $2}')
if [ -n "$JAVA_PID" ]; then
    echo "找到Java进程PID: $JAVA_PID，正在停止..."
    kill -9 $JAVA_PID
    echo "Java服务已停止"
else
    echo "未找到运行的Java服务"
fi

echo "==================================="
echo "服务停止完成"
echo "==================================="
