package com.autoagent.ai_live_master.scriptRecommendation.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.model.PageRequestVO;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptRecommendation.dto.EcommerceBenefitsDTO;
import com.autoagent.ai_live_master.scriptRecommendation.entity.EcommerceBenefits;
import com.autoagent.ai_live_master.scriptRecommendation.service.EcommerceBenefitsService;
import com.autoagent.ai_live_master.scriptRecommendation.vo.EcommerceBenefitsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * 电商权益控制器
 */
@Slf4j
@Tag(name = "10-电商权益管理", description = "电商权益的创建、查询和管理相关接口")
@RestController
@RequestMapping("/ecommerce-benefits")
public class EcommerceBenefitsController {

    @Autowired
    private EcommerceBenefitsService ecommerceBenefitsService;

    @Operation(summary = "创建电商权益", description = "创建新的电商权益记录")
    @PostMapping
    public ApiResponse<EcommerceBenefitsDTO> create(
            @Parameter(description = "电商权益信息", required = true)
            @Valid @RequestBody EcommerceBenefitsVO vo) {
        try {
            EcommerceBenefits entity = BeanConverter.convert(vo, EcommerceBenefits.class);
            entity.setUserId(UserContext.getCurrentUserId());
            return ApiResponse.success(ecommerceBenefitsService.createEcommerceBenefits(entity));
        } catch (Exception e) {
            log.error("创建电商权益失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "创建电商权益失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取电商权益列表", description = "分页获取当前用户的所有电商权益，支持按权益名称模糊搜索")
    @GetMapping("/list")
    public ApiResponse<Page<EcommerceBenefitsDTO>> list(@ModelAttribute PageRequestVO pageRequest) {
        try {
            Long currentUserId = UserContext.getCurrentUserId();
            log.info("获取电商权益列表 - 用户ID: {}, 页码: {}, 每页大小: {}, 关键字: {}",
                    currentUserId, pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());
            Page<EcommerceBenefitsDTO> benefits = ecommerceBenefitsService.getPageByUserId(
                    currentUserId,
                    pageRequest.getPageNum(),
                    pageRequest.getPageSize(),
                    pageRequest.getKeyword());
            return ApiResponse.success(benefits);
        } catch (Exception e) {
            log.error("获取电商权益列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取电商权益列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新电商权益", description = "更新指定ID的电商权益信息")
    @PutMapping("/{id}")
    public ApiResponse<EcommerceBenefitsDTO> update(
            @Parameter(description = "电商权益ID", required = true)
            @PathVariable Integer id,
            @Parameter(description = "电商权益信息", required = true)
            @Valid @RequestBody EcommerceBenefitsVO vo) {
        try {
            EcommerceBenefits entity = BeanConverter.convert(vo, EcommerceBenefits.class);
            entity.setId(id);
            entity.setUserId(UserContext.getCurrentUserId());
            return ApiResponse.success(ecommerceBenefitsService.updateEcommerceBenefits(entity));
        } catch (Exception e) {
            log.error("更新电商权益失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "更新电商权益失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除电商权益", description = "删除指定ID的电商权益")
    @DeleteMapping("/{id}")
    public ApiResponse<Boolean> delete(
            @Parameter(description = "电商权益ID", required = true)
            @PathVariable Integer id) {
        try {
            return ApiResponse.success(ecommerceBenefitsService.deleteEcommerceBenefits(id));
        } catch (Exception e) {
            log.error("删除电商权益失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "删除电商权益失败: " + e.getMessage());
        }
    }
}