package com.autoagent.ai_live_master.scriptGeneration.service.impl;


import com.autoagent.ai_live_master.common.model.Payload;
import com.autoagent.ai_live_master.common.utils.AgentClient;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.common.utils.KbClient;

import com.autoagent.ai_live_master.scriptGeneration.dto.ProductMaterialDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.ProductMaterials;
import com.autoagent.ai_live_master.scriptGeneration.mapper.ProductMaterialsMapper;


import com.autoagent.ai_live_master.scriptGeneration.service.ProductMaterialsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class ProductMaterialsServiceImpl implements ProductMaterialsService {
    
    @Autowired
    private ProductMaterialsMapper mapper;
    
    @Autowired
    @Qualifier("productSearchAgentClient")
    private AgentClient agentClient;

    @Autowired
    private KbClient kbClient;

    @Override
    public void addMaterial(ProductMaterialDTO uploadDTO) {
        log.info("开始处理文件上传 - 用户ID: {}, KB ID: {}", 
            uploadDTO.getUserId(), uploadDTO.getKbId());
        
        try {
            log.debug("已设置KbClient认证信息");
            String fileName = uploadDTO.getFile().getOriginalFilename();
            log.info("准备上传文件: {}, 大小: {} bytes", 
                fileName, uploadDTO.getFile().getSize());
            
            byte[] fileContent = uploadDTO.getFile().getBytes();
            Map<String, Object> result = kbClient.uploadFileToKb(
                uploadDTO.getKbId().intValue(), 
                fileContent, 
                fileName
            );
            Map<String, Object> data= (Map<String, Object>) result.get("data");
            // 使用BeanConverter创建并转换基本属性
            ProductMaterials material = BeanConverter.convert(uploadDTO, ProductMaterials.class);
            // 设置额外属性
            material.setFileId((String) data.get("fileId"));
            material.setFileName(fileName);
            mapper.insert(material);
            
            log.info("文件上传成功 - KB返回结果: {}, 数据库ID: {}", result, material.getId());
        } catch (Exception e) {
            log.error("文件上传处理失败 - 用户ID: {}, 错误: {}", 
                uploadDTO.getUserId(), e.getMessage(), e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public String searchProductMaterials(String query) {
        log.info("开始产品信息检索 - 查询内容: {}", query);
        
        try {
            // 构建请求payload
            log.debug("构建Agent请求payload - AgentId: {}", agentClient.getAgentId());
            Payload payload = Payload.builder()
                    .agentId(agentClient.getAgentId())
                    .userChatInput(query)
                    .build();
            
            // 调用Agent服务
            log.info("发送检索请求到Agent服务");
            String response = agentClient.chat_completions(payload);
            
            // 检查响应
            if (response == null || response.isEmpty()) {
                log.warn("Agent服务返回空响应");
                return "未找到相关产品信息";
            }
            
            log.info("产品信息检索成功 - 响应长度: {}", response.length());
            return response;
            
        } catch (Exception e) {
            log.error("产品信息检索失败 - 查询内容: {}, 错误信息: {}", query, e.getMessage(), e);
            throw new RuntimeException("产品信息检索失败: " + e.getMessage(), e);
        }
    }
}