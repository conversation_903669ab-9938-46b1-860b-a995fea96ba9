package com.autoagent.ai_live_master.scriptGeneration.controller;

import com.autoagent.ai_live_master.common.base.ApiResponse;
import com.autoagent.ai_live_master.common.base.UserContext;
import com.autoagent.ai_live_master.common.utils.BeanConverter;
import com.autoagent.ai_live_master.scriptGeneration.dto.ScriptFeaturesDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.ScriptFeatures;
import com.autoagent.ai_live_master.scriptGeneration.service.ScriptFeaturesService;
import com.autoagent.ai_live_master.scriptGeneration.vo.PageRequestVO;
import com.autoagent.ai_live_master.scriptGeneration.vo.ScriptFeaturesUploadVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "04-话术特征管理", description = "话术特征的上传和分析相关接口")
@RestController
@RequestMapping("/script-features")
public class ScriptFeaturesController {

    @Autowired
    private ScriptFeaturesService scriptFeaturesService;

    @Operation(summary = "上传话术特征文件", description = "上传话术文件并分析其特征，用于后续话术生成")
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public ApiResponse<String> add(
            @Parameter(description = "话术特征文件", required = true)
            @ModelAttribute ScriptFeaturesUploadVO uploadVO) {
        try {
            log.info("接收到文件上传请求");
            Long currentUserId = UserContext.getCurrentUserId();

            // 使用BeanConverter转换VO为DTO
            log.info("转换VO为DTO");

            ScriptFeaturesDTO dto = BeanConverter.convert(uploadVO, ScriptFeaturesDTO.class);
            dto.setUserId(currentUserId);

            log.info("调用服务方法添加话术脚本");
            scriptFeaturesService.addFeature(dto);

            return ApiResponse.success("文件上传成功");
        } catch (Exception e) {
            log.error("添加脚本特征失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "添加脚本特征失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取当前用户的话术特征列表", description = "分页获取当前登录用户的所有话术特征记录")
    @GetMapping("/processed-list")
    public ApiResponse<Page<ScriptFeatures>> getFeaturesList(@ModelAttribute PageRequestVO pageRequest) {
        try {
            log.info("获取当前用户的话术特征列表 - 页码: {}, 每页大小: {}", pageRequest.getPageNum(), pageRequest.getPageSize());
            Long currentUserId = UserContext.getCurrentUserId();
            Page<ScriptFeatures> features = scriptFeaturesService.getPageByUserId(currentUserId, pageRequest.getPageNum(), pageRequest.getPageSize());
            return ApiResponse.success(features);
        } catch (Exception e) {
            log.error("获取话术特征列表失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取话术特征列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据ID获取话术特征", description = "根据话术特征ID获取详细信息")
    @GetMapping("/processed/{id}")
    public ApiResponse<ScriptFeatures> getFeatureById(
            @Parameter(description = "话术特征ID", required = true)
            @PathVariable Long id) {
        try {
            log.info("根据ID获取话术特征 - ID: {}", id);
            ScriptFeatures feature = scriptFeaturesService.getFeatureById(id);
            if (feature == null) {
                return ApiResponse.error(404, "未找到指定的话术特征");
            }
            return ApiResponse.success(feature);
        } catch (Exception e) {
            log.error("获取话术特征失败 - 错误信息: {}", e.getMessage(), e);
            return ApiResponse.error(500, "获取话术特征失败: " + e.getMessage());
        }
    }
}