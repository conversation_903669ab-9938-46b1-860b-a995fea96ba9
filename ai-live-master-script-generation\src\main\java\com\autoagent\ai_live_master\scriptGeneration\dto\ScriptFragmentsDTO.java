package com.autoagent.ai_live_master.scriptGeneration.dto;

import lombok.Data;

import java.util.List;

@Data
public class ScriptFragmentsDTO {
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 产品描述
     */
    private String productDesc;
    
    /**
     * 参考脚本ID列表
     */
    private List<Long> referenceScriptIds;
    
    /**
     * 话术大纲
     */
    private String scriptOutline;
    
    /**
     * 当前选择的文本
     */
    private String currentSelection;
    
    /**
     * 当前内容
     */
    private String currentContent;
    
    /**
     * 用户生成的特殊要求
     */
    private String userGeneratedRequest;
}