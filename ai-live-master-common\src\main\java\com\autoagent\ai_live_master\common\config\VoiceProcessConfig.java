package com.autoagent.ai_live_master.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 语音处理配置类
 * 用于从配置文件中读取阿里云语音识别服务相关配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.voice")
public class VoiceProcessConfig {
    /**
     * 阿里云访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 阿里云访问密钥Secret
     */
    private String accessKeySecret;
    
    /**
     * 应用密钥
     */
    private String appKey;
    
    /**
     * 区域ID，固定值
     */
    private String regionId = "cn-beijing";
    
    /**
     * 终端名称，固定值
     */
    private String endpointName = "cn-beijing";
    
    /**
     * 产品名称，固定值
     */
    private String product = "nls-filetrans";
    
    /**
     * 域名，固定值
     */
    private String domain = "filetrans.cn-beijing.aliyuncs.com";
    
    /**
     * API版本，固定值
     */
    private String apiVersion = "2018-08-17";
}