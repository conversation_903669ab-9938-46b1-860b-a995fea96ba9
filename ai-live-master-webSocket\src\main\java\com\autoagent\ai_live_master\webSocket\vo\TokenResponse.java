package com.autoagent.ai_live_master.webSocket.vo;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Token响应结果")
public class TokenResponse {
    
    @Schema(description = "Token值")
    private String token;
    
    @Schema(description = "过期时间戳（秒）")
    private Long expireTime;
    
    @Schema(description = "过期时间（北京时间）")
    private String expireDate;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }
} 