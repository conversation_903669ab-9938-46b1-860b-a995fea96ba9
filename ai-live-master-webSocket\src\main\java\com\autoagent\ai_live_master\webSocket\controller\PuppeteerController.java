package com.autoagent.ai_live_master.webSocket.controller;


import com.autoagent.ai_live_master.webSocket.service.PuppeteerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Tag(name = "Puppeteer控制", description = "用于控制Puppeteer自动化操作的API")
@RestController
@RequestMapping("/live/puppeteer")
@RequiredArgsConstructor
public class PuppeteerController {

    private final PuppeteerService puppeteerService;

    @Value("${puppeteer.ws-base-url}")
    private String wsBaseUrl;

    @Operation(
            summary = "连接直播间",
            description = "通过Puppeteer自动连接直播间并设置转发（后端自动拼接 WebSocket 地址）"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "连接成功"),
            @ApiResponse(responseCode = "400", description = "连接失败")
    })
    @PostMapping("/connect")
    public ResponseEntity<String> connect(
            @Parameter(description = "直播间房间号", required = true) @RequestParam String roomNumber) {
        try {
            puppeteerService.connect(roomNumber);
            return ResponseEntity.ok("连接成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("连接失败: " + e.getMessage());
        }
    }


    @Operation(
        summary = "断开连接",
        description = "断开Puppeteer连接并关闭浏览器"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "断开连接成功"),
        @ApiResponse(responseCode = "400", description = "断开连接失败")
    })
    @PostMapping("/disconnect")
    public ResponseEntity<String> disconnect() {
        try {
            puppeteerService.disconnect();
            return ResponseEntity.ok("断开连接成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("断开连接失败: " + e.getMessage());
        }
    }
} 