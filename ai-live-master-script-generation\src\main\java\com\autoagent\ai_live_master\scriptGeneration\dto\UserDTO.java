package com.autoagent.ai_live_master.scriptGeneration.dto;

import com.autoagent.ai_live_master.scriptGeneration.entity.User;
import lombok.Data;

/**
 * 用户数据传输对象，用于返回给前端，不包含敏感信息
 */
@Data
public class UserDTO {
    private Integer id;
    private String userid;
    
    // 用于从User实体转换为DTO的静态方法
    public static UserDTO fromEntity(User user) {
        if (user == null) {
            return null;
        }
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUserid(user.getUserid());
        return dto;
    }
}