package com.autoagent.ai_live_master.scriptGeneration.service.impl;


import com.autoagent.ai_live_master.common.model.Payload;
import com.autoagent.ai_live_master.common.utils.AgentClient;
import com.autoagent.ai_live_master.common.utils.KbClient;
import com.autoagent.ai_live_master.common.utils.OssUtil;

import com.autoagent.ai_live_master.scriptGeneration.dto.MethodologySummaryDTO;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologyFile;
import com.autoagent.ai_live_master.scriptGeneration.entity.MethodologySummary;
import com.autoagent.ai_live_master.scriptGeneration.mapper.MethodologyFileMapper;
import com.autoagent.ai_live_master.scriptGeneration.mapper.MethodologySummaryMapper;

import com.autoagent.ai_live_master.scriptGeneration.service.MethodologySummaryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class MethodologySummaryServiceImpl implements MethodologySummaryService {
    
    @Autowired
    private MethodologySummaryMapper mapper;

    @Autowired
    private MethodologyFileMapper methodologyFileMapper;

    @Autowired
    @Qualifier("methodologyAgentClient")
    private AgentClient agentClient;

    @Autowired
    private KbClient kbClient;

    @Autowired
    private OssUtil ossUtil;

    @Override
    public void addSummary(MethodologySummaryDTO dto) {
        log.info("获取最新的technique_summary");
        MethodologySummary latestSummary = mapper.getLatestByUserId(dto.getUserId());
        String techniqueSummary = latestSummary != null ? latestSummary.getTechniqueSummary() : "";

        log.info("开始上传文件到OSS");
        String ossFilePath = null;
        try {
            // 上传文件到OSS
            ossFilePath = ossUtil.uploadLargeFile(dto.getFile());
            log.info("文件上传到OSS成功，下载路径: {}", ossFilePath);

            // 保存文件记录到methodology_files表
            MethodologyFile methodologyFile = new MethodologyFile();
            methodologyFile.setUserId(dto.getUserId());
            methodologyFile.setFileName(dto.getFile().getOriginalFilename());
            methodologyFile.setFilePath(ossFilePath);
            methodologyFile.setRemarks("方法论总结文件");
            methodologyFileMapper.insert(methodologyFile);
            log.info("文件记录保存成功，文件ID: {}", methodologyFile.getId());

        } catch (Exception e) {
            log.error("文件上传到OSS失败", e);
//            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }

        log.info("上传文件到知识库");
        try {
            String fileId = kbClient.uploadFile(dto.getFile().getBytes(), dto.getFile().getOriginalFilename(),"id",null);
            log.info("上传文件id，{}", fileId);

            Map<String, String> stateMap = new HashMap<>();
            stateMap.put("script", techniqueSummary);
            stateMap.put("user_id", String.valueOf(dto.getUserId()));

            log.info("准备payload数据");
            Payload payload = Payload.builder()
                    .agentId(agentClient.getAgentId())
                    .userChatInput("总结方法论")
                    .files(Collections.singletonList(new Payload.FileInfo(fileId, dto.getFile().getOriginalFilename(), null, null)))
                    .state(stateMap)
                    .build();

            log.info("调用Agent处理");
            String requestId = agentClient.chat_completions_async(payload);

            // 异步处理
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始异步查询 agent 结果");
                    String newTechniqueSummary = agentClient.chat_completions_async_polling(requestId);
                    log.info("异步查询 agent 结果成功，返回方法论总结：{}", newTechniqueSummary);

                    MethodologySummary newSummary = new MethodologySummary();
                    newSummary.setUserId(dto.getUserId());
                    newSummary.setTechniqueSummary(newTechniqueSummary);
                    mapper.insert(newSummary);
                } catch (Exception e) {
                    log.error("异步调用 agent 处理失败", e);
                    throw new RuntimeException("异步调用 agent 处理失败: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            log.error("处理方法论总结失败", e);
            // 如果知识库处理失败，但OSS上传成功，文件记录已保存，不需要回滚OSS文件
            throw new RuntimeException("处理方法论总结失败: " + e.getMessage());
        }
    }

    @Override
    public Page<MethodologySummary> getPageByUserId(Long userId, Integer pageNum, Integer pageSize) {
        log.info("分页获取用户的方法论总结列表 - 用户ID: {}, 页码: {}, 每页大小: {}", userId, pageNum, pageSize);
        LambdaQueryWrapper<MethodologySummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MethodologySummary::getUserId, userId)
               .orderByDesc(MethodologySummary::getId);
        Page<MethodologySummary> page = new Page<>(pageNum, pageSize);
        return mapper.selectPage(page, wrapper);
    }

    @Override
    public MethodologySummary getSummaryById(Long id) {
        log.info("根据ID获取方法论总结 - ID: {}", id);
        return mapper.selectById(id);
    }
}