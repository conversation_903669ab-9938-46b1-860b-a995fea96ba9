package com.autoagent.ai_live_master.scriptGeneration.Enum;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum FileStatusEnum {
    PENDING("等待处理"),
    PROCESSING("处理中"),
    SUCCESS("成功"),
    FAILED("失败");

    @JsonValue
    @EnumValue
    private final String value;

    FileStatusEnum(String value) {
        this.value = value;
    }

    // 根据数据库的字符串转枚举，方便读取
    public static FileStatusEnum fromValue(String value) {
        for (FileStatusEnum status : FileStatusEnum.values()) {
            if (status.value.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的状态值: " + value);
    }
}

