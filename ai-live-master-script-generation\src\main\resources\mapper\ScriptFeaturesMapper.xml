<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autoagent.ai_live_master.scriptGeneration.mapper.ScriptFeaturesMapper">

    <select id="getLatestByUserId" resultType="com.autoagent.ai_live_master.scriptGeneration.entity.ScriptFeatures">
        SELECT * FROM script_features
        WHERE user_id = #{userId}
        ORDER BY id DESC
        LIMIT 1
    </select>

</mapper>